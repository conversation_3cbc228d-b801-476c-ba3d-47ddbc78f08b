import { ContactForm } from "@/components/contact-form";
import { Mail, MapPin, Phone } from "lucide-react";

export default function ContactPage() {
  return (
    <section className="container mx-auto py-12 px-4 min-h-screen">
      <div className="max-w-8xl mx-auto">
        <h1 className="text-4xl md:text-5xl font-bold mb-8 text-center text-primary">Contact Us</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-secondary dark:bg-card border border-input rounded-2xl p-8 shadow-lg backdrop-blur-sm">
            <h2 className="text-2xl font-bold mb-4">Get In Touch</h2>
            <p className="mb-4">
              Have questions about our platform or need assistance with MCP servers? We&apos;re here
              to help!
            </p>
            <div className="mt-6">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full border border-foreground flex items-center justify-center mr-4">
                  <Mail className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">Email</p>
                  <p><EMAIL></p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full border border-foreground flex items-center justify-center mr-4">
                  <Phone className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">Phone</p>
                  <p>+****************</p>
                </div>
              </div>
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full border border-foreground flex items-center justify-center mr-4">
                  <MapPin className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">Address</p>
                  <p>123 Tech Plaza, San Francisco, CA 94107</p>
                </div>
              </div>
            </div>
          </div>
          <ContactForm />
        </div>
      </div>
    </section>
  );
}
