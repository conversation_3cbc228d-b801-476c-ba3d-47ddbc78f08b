import { RegistrationForm } from "@/components/registration-form";
import Link from "next/link";

export default function SignupPage() {
  return (
    <div className="container mx-auto px-4 py-12 min-h-screen">
      <div className="max-w-md mx-auto py-12">
        <div className="text-center mb-8">
          <h1 className="text-4xl text-primary font-bold">Create Account</h1>
          <p className="text-foreground/50 mt-2">Join the MCP Marketplace community</p>
        </div>
        <div className="bg-secondary dark:bg-card border border-input rounded-2xl p-8 shadow-lg backdrop-blur-sm">
          <RegistrationForm />
          <div className="mt-6 text-center">
            <p className="text-foreground/40">
              Already have an account?{" "}
              <Link href="/auth/login" className="text-primary hover:underline">
                Log in
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
