"use client";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { useEffect } from "react";

export default function ConfirmationPage({
  searchParams,
}: {
  searchParams: { message: string; code?: string };
}) {
  const message = searchParams.message;
  const code = searchParams.code;

  useEffect(() => {
    // Store the current path without search params
    const currentPath = window.location.pathname;

    // Update the URL without triggering a navigation
    window.history.replaceState({}, "", currentPath);
  }, []);

  return (
    <div className="flex justify-center">
      <div className="text-center w-[30rem] p-4 mt-10 h-fit">
        <p>{message}</p>
        {!code && (
          <Link href="/auth/login" className={`${buttonVariants({ variant: "link" })} mt-2`}>
            Log In
          </Link>
        )}
      </div>
    </div>
  );
}
