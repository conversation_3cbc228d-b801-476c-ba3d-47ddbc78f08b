import { LoginForm } from "@/components/login-form";
import Link from "next/link";

export default function LoginPage() {
  return (
    <div className="container mx-auto px-4 py-12 min-h-screen">
      <div className="max-w-md mx-auto py-16">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-primary">Welcome Back</h1>
          <p className="text-foreground/50 mt-2">Log in to your account</p>
        </div>
        <div className="bg-secondary dark:bg-card border border-input rounded-2xl p-8 shadow-lg backdrop-blur-sm">
          <LoginForm />
          <div className="mt-6 text-center">
            <p className="text-foreground/40">
              Don&apos;t have an account?{" "}
              <Link href="/auth/registration" className="text-primary hover:underline">
                Register
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
