export default function AboutPage() {
  return (
    <section className="container mx-auto py-12 px-4 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl md:text-5xl font-bold mb-8 text-center text-primary">About Us</h1>
        <div className="bg-secondary dark:bg-card rounded-2xl p-8 border border-input shadow-lg backdrop-blur-sm">
          <p className="text-lg mb-6">
            Welcome to the MCP Marketplace, the premier destination for discovering and integrating
            Model Context Protocol servers. Our platform connects developers and businesses with
            cutting-edge MCP solutions to enhance their projects.
          </p>
          <p className="text-lg mb-6">
            Our mission is to simplify the process of finding, evaluating, and implementing MCP
            servers, making advanced technology accessible to everyone. We believe in fostering a
            community where innovation thrives through collaboration and knowledge sharing.
          </p>
          <h2 className="text-2xl font-bold mt-10 mb-4">Our Vision</h2>
          <p className="text-lg mb-6">
            We envision a world where integrating sophisticated model context protocols is seamless
            and intuitive. By providing a centralized marketplace with comprehensive information and
            direct communication channels, we aim to accelerate technological advancement across
            industries.
          </p>
          <h2 className="text-2xl font-bold mt-10 mb-4">The Team</h2>
          <p className="text-lg">
            Our team consists of passionate engineers, designers, and industry experts dedicated to
            creating the best possible platform for the MCP community. With decades of combined
            experience in protocol implementation and software development, we understand the
            challenges and opportunities in this space.
          </p>
        </div>
      </div>
    </section>
  );
}
