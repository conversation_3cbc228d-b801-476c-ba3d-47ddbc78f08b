@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-amber: var(--amber);
  --color-crimson: var(--crimson);
  --color-teal: var(--teal);
}

:root {
  --radius: 0.625rem;
  --background: oklch(90% 0 0deg);
  --foreground: oklch(14.1% 0.005 285.823deg);
  --bg-pattern-opacity: 30%;
  --hexagon-opacity: 5%;
  --card: oklch(100% 0 0deg);
  --card-foreground: oklch(14.1% 0.005 285.823deg);
  --popover: oklch(100% 0 0deg);
  --popover-foreground: oklch(14.1% 0.005 285.823deg);
  --primary: oklch(70.5% 0.18 47.604deg); /* change this for different color */
  --primary-foreground: oklch(98.5% 0 0deg);
  --secondary: oklch(96.7% 0.001 286.375deg);
  --secondary-foreground: oklch(21% 0.006 285.885deg);
  --muted: oklch(96.7% 0.001 286.375deg);
  --muted-foreground: oklch(55.2% 0.016 285.938deg);
  --accent: oklch(96.7% 0.001 286.375deg);
  --accent-foreground: oklch(21% 0.006 285.885deg);
  --destructive: oklch(57.7% 0.245 27.325deg);
  --border: oklch(92% 0.004 286.32deg);
  --input: oklch(92% 0.004 286.32deg);
  --ring: oklch(70.5% 0.015 286.067deg);
  --chart-1: oklch(64.6% 0.222 41.116deg);
  --chart-2: oklch(60% 0.118 184.704deg);
  --chart-3: oklch(39.8% 0.07 227.392deg);
  --chart-4: oklch(82.8% 0.189 84.429deg);
  --chart-5: oklch(76.9% 0.188 70.08deg);
  --sidebar: oklch(98.5% 0 0deg);
  --sidebar-foreground: oklch(14.1% 0.005 285.823deg);
  --sidebar-primary: oklch(21% 0.006 285.885deg);
  --sidebar-primary-foreground: oklch(98.5% 0 0deg);
  --sidebar-accent: oklch(96.7% 0.001 286.375deg);
  --sidebar-accent-foreground: oklch(21% 0.006 285.885deg);
  --sidebar-border: oklch(92% 0.004 286.32deg);
  --sidebar-ring: oklch(70.5% 0.015 286.067deg);
}

.dark {
  --background: oklch(14.1% 0.005 285.823deg);
  --foreground: oklch(98.5% 0 0deg);
  --bg-pattern-opacity: 0%;
  --hexagon-opacity: 5%;
  --card: oklch(21% 0.006 285.885deg);
  --card-foreground: oklch(98.5% 0 0deg);
  --popover: oklch(21% 0.006 285.885deg);
  --popover-foreground: oklch(98.5% 0 0deg);
  --primary: oklch(70.5% 0.17 47.604deg); /* change this for different color 60.5% 0.14 20.604deg */
  --primary-foreground: oklch(21% 0.006 285.885deg);
  --secondary: oklch(27.4% 0.006 286.033deg);
  --secondary-foreground: oklch(98.5% 0 0deg);
  --muted: oklch(27.4% 0.006 286.033deg);
  --muted-foreground: oklch(70.5% 0.015 286.067deg);
  --accent: oklch(27.4% 0.006 286.033deg);
  --accent-foreground: oklch(98.5% 0 0deg);
  --destructive: oklch(70.4% 0.191 22.216deg);
  --border: oklch(100% 0 0deg / 10%);
  --input: oklch(100% 0 0deg / 15%);
  --ring: oklch(55.2% 0.016 285.938deg);
  --chart-1: oklch(48.8% 0.243 264.376deg);
  --chart-2: oklch(69.6% 0.17 162.48deg);
  --chart-3: oklch(76.9% 0.188 70.08deg);
  --chart-4: oklch(62.7% 0.265 303.9deg);
  --chart-5: oklch(64.5% 0.246 16.439deg);
  --sidebar: oklch(21% 0.006 285.885deg);
  --sidebar-foreground: oklch(98.5% 0 0deg);
  --sidebar-primary: oklch(48.8% 0.243 264.376deg);
  --sidebar-primary-foreground: oklch(98.5% 0 0deg);
  --sidebar-accent: oklch(27.4% 0.006 286.033deg);
  --sidebar-accent-foreground: oklch(98.5% 0 0deg);
  --sidebar-border: oklch(100% 0 0deg / 10%);
  --sidebar-ring: oklch(55.2% 0.016 285.938deg);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;

    background-attachment: fixed;
    background-image:
      radial-gradient(
        circle at 20% 80%,
        oklch(96% 0.05 280deg / var(--bg-pattern-opacity)) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 20%,
        oklch(97% 0.03 30deg / var(--bg-pattern-opacity)) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 40% 40%,
        oklch(97% 0.03 320deg / var(--bg-pattern-opacity)) 0%,
        transparent 50%
      ),
      linear-gradient(
        135deg,
        oklch(99% 0.01 270deg / var(--bg-pattern-opacity)) 0%,
        oklch(98% 0.02 240deg / 30%) 100%
      );
  }
}

/* Custom animations */
@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }

  50% {
    transform: translateY(-10px) rotate(180deg);
  }

  100% {
    transform: translateY(0) rotate(360deg);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out forwards;
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out forwards;
}

@keyframes bounce-subtle {
  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-4px);
  }
}

.animate-bounce-subtle {
  animation: bounce-subtle 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-glow {
  animation: pulse-glow 4s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  animation: shimmer 3s infinite;
  background: linear-gradient(
    90deg,
    oklch(100% 0 0deg / 0%) 0%,
    oklch(100% 0 0deg / 5%) 50%,
    oklch(100% 0 0deg / 0%) 100%
  );
  background-size: 200% 100%;
}

/* Fancy border */
.fancy-border {
  background:
    linear-gradient(var(--secondary), var(--secondary)) padding-box,
    linear-gradient(
        to right,
        var(--primary),
        oklch(77% 0.1647 70.08deg),
        oklch(64% 0.2078 25.33deg)
      )
      border-box;
  border: 1px solid transparent;
  border-radius: var(--radius);
  position: relative;
  transition: all 0.3s ease;
}

.fancy-border:hover {
  box-shadow: 0 0 20px oklch(70% 0.123 182.5deg / 20%);
}

/* Glass effect */
.glass-effect {
  backdrop-filter: blur(12px);
  background: oklch(21% 0.0398 265.75deg / 60%);
  border: 1px solid oklch(100% 0 0deg / 10%);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

::-webkit-scrollbar-track {
  background: oklch(21% 0.0398 265.75deg / 20%);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: oklch(70% 0.123 182.5deg / 50%);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: oklch(70% 0.123 182.5deg / 80%);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(
    to right,
    var(--primary),
    oklch(70.5% 0.15 47.604deg),
    oklch(70.5% 0.13 47.604deg)
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
}

/* Section dividers */
.section-divider {
  height: 120px;
  margin-bottom: -60px;
  margin-top: -60px;
  overflow: hidden;
  position: relative;
  z-index: 10;
}

.section-divider::before {
  background: linear-gradient(135deg, transparent 49.5%, var(--secondary) 50%);
  content: "";
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.section-divider.inverted::before {
  background: linear-gradient(-45deg, transparent 49.5%, var(--background) 50%);
}

/* Hexagon pattern - Light and Dark mode */
.hexagon-pattern {
  background-image:
    linear-gradient(
      to right,
      oklch(70% 0.123 182.5deg / var(--hexagon-opacity)) 1px,
      transparent 1px
    ),
    linear-gradient(
      to bottom,
      oklch(70% 0.123 182.5deg / var(--hexagon-opacity)) 1px,
      transparent 1px
    );
  background-size: 30px 30px;
}
