"use client";

import MCPServerList from "@/components/mcp-server-list";
import { useChatToggle } from "@/lib/providers/chat-toggle-provider";
import { Button } from "@/components/ui/button";
import { cn } from "@/utils/utils";

export default function Home() {
  const { isClosed, setIsClosed } = useChatToggle();
  // code font - font-[family-name:var(--font-geist-mono)]
  return (
    <div className="relative font-[family-name:var(--font-geist-sans)]">
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none z-0">
        <div className="absolute top-[10%] left-[5%] w-[30rem] h-[30rem] bg-teal/10 rounded-full filter blur-[100px] animate-float"></div>
        <div
          className="absolute bottom-[20%] right-[10%] w-[25rem] h-[25rem] bg-amber/10 rounded-full filter blur-[100px] animate-float"
          style={{ animationDelay: "-2s" }}
        ></div>
        <div
          className="absolute top-[40%] right-[20%] w-[20rem] h-[20rem] bg-crimson/10 rounded-full filter blur-[100px] animate-float"
          style={{ animationDelay: "-4s" }}
        ></div>
      </div>

      {/* Main content */}
      <div className="relative z-10">
        {/* Hero section */}
        <section className="py-20 md:py-32 min-h-[95vh] flex items-center hexagon-pattern relative overflow-hidden">
          <div className="container px-4 mx-auto relative z-10">
            <div className="text-center mb-16">
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 mb-8">
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-primary">AI-Powered MCP Discovery</span>
              </div>

              <h1 className="text-6xl md:text-8xl font-bold mb-8 leading-tight">
                <span className="gradient-text">MCP Marketplace</span>
              </h1>

              <p className="text-xl md:text-2xl text-foreground/80 max-w-4xl mx-auto leading-relaxed mb-4">
                Discover, connect, and chat with the perfect
                <span className="text-primary font-semibold"> Model Context Protocol</span> servers
                for your projects
              </p>

              <p className="text-lg text-foreground/60 max-w-2xl mx-auto mb-12">
                Powered by advanced AI to help you find exactly what you need
              </p>
            </div>

            <div className="max-w-2xl mx-auto">
              <div className="flex flex-col sm:flex-row justify-center gap-4 mb-8">
                <Button
                  size="lg"
                  className={cn(
                    "px-8 py-4 h-16 bg-primary text-primary-foreground rounded-xl font-semibold text-lg hover:bg-primary/90 dark:hover:bg-primary/90 transition-all duration-300 shadow-lg dark:hover:shadow-xl hover:shadow-xl dark:hover:shadow-primary/25 hover:shadow-primary/25 transform hover:-translate-y-1",
                  )}
                >
                  <span className="relative z-10">Browse All Servers</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </Button>

                <Button
                  variant={"outline"}
                  size={"lg"}
                  onClick={() => setIsClosed(false)}
                  disabled={!isClosed}
                  className={cn(
                    "group px-12 py-4 h-16 border-2 border-primary/30 text-primary rounded-xl font-semibold text-lg dark:bg-transparent dark:hover:border-primary dark:hover:bg-primary/5 hover:border-primary hover:bg-primary/5 transition-all duration-300 backdrop-blur-sm",
                  )}
                >
                  <span className="flex items-center gap-2">Start Chat</span>
                </Button>
              </div>

              <div className="flex items-center justify-center gap-8 text-sm text-foreground/60">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>1000+ Servers</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>AI-Powered</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span>Open Source</span>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced floating elements */}
          <div className="absolute top-20 left-10 w-20 h-20 bg-primary/10 rounded-full blur-xl animate-float"></div>
          <div
            className="absolute bottom-20 right-10 w-32 h-32 bg-amber/10 rounded-full blur-xl animate-float"
            style={{ animationDelay: "-2s" }}
          ></div>
          <div
            className="absolute top-1/2 right-20 w-16 h-16 bg-teal/10 rounded-full blur-xl animate-float"
            style={{ animationDelay: "-4s" }}
          ></div>
        </section>

        {/* Section divider */}
        <div className="section-divider bg-gradient-to-r from-primary/5 via-transparent to-primary/5"></div>

        {/* All servers section */}
        <section className="py-32 relative bg-gradient-to-br from-secondary via-secondary/95 to-secondary/90 overflow-hidden">
          {/* Background pattern */}
          <div className="absolute inset-0 opacity-30">
            <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.1),transparent_50%)]"></div>
            <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_70%_80%,rgba(255,119,198,0.1),transparent_50%)]"></div>
          </div>

          <div className="container px-4 mx-auto relative z-10">
            <div className="max-w-4xl mx-auto text-center mb-20">
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 mb-8">
                <svg
                  className="w-4 h-4 text-primary"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                  />
                </svg>
                <span className="text-sm font-medium text-primary">Server Marketplace</span>
              </div>

              <h2 className="text-5xl md:text-6xl font-bold mb-8 leading-tight">
                <span className="gradient-text">Explore MCP Servers</span>
              </h2>

              <p className="text-xl md:text-2xl text-foreground/70 leading-relaxed mb-6">
                Browse our curated collection of Model Context Protocol servers, each designed to
                enhance your AI workflows
              </p>

              <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-foreground/60">
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span>Verified & Tested</span>
                </div>
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Community Rated</span>
                </div>
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span>Developer Friendly</span>
                </div>
              </div>
            </div>

            <MCPServerList />
          </div>
        </section>
      </div>
    </div>
  );
}
