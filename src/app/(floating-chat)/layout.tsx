"use client";

import Header from "@/components/header";
import Footer from "@/components/footer";
import { useUser } from "@/lib/providers/user-provider";
import { UserChatInterface } from "@/components/chat-interface-user";
import { VisitorChatInterface } from "@/components/chat-interface-visitor";
import { isLocalStorageAvailable, readFromLocalStorage } from "@/lib/local-storage";
import { useEffect, useState } from "react";
import { Chat, VisitorChat } from "@/lib/types";
import { reviveChatDates } from "@/utils/utils";
import { activeChatSession } from "@/lib/active-chat-session";
import { getChat } from "@/lib/supabase-queries";
import ChatInterfaceSkeleton from "@/components/chat-interface-skeleton";

export default function SiteLayout({ children }: { children: React.ReactNode }) {
  const { user } = useUser();
  const [key, setKey] = useState<string>("");
  const [userChat, setUserChat] = useState<Chat | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [visitorChat, setVisitorChat] = useState<VisitorChat | null>(() => {
    if (!user) {
      if (typeof window !== "undefined" && isLocalStorageAvailable()) {
        const rawChat = readFromLocalStorage<VisitorChat | null>("visitorChat");
        if (rawChat) {
          return reviveChatDates(rawChat);
        }
      }
      return null;
    }
    return null;
  });

  useEffect(() => {
    if (user) {
      setIsLoading(true);
      setKey(`${user.id}-activeChat`);
      const id = activeChatSession.getChatId(`${user.id}-activeChat`);
      if (id) {
        getChat(id, user.id).then((chat) => {
          setUserChat(chat);
        });
      } else {
        setUserChat(null);
      }
      setIsLoading(false);
    }
  }, [user]);

  return (
    <>
      <Header />
      {children}
      {user ? (
        isLoading ? (
          <ChatInterfaceSkeleton />
        ) : (
          <UserChatInterface chat={userChat} storageKey={key} />
        )
      ) : (
        <VisitorChatInterface visitorChat={visitorChat} setVisitorChat={setVisitorChat} />
      )}
      <Footer />
    </>
  );
}
