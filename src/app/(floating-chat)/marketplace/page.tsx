import MCPServerList from "@/components/mcp-server-list";
import Link from "next/link";

export default function Home() {
  // code font - font-[family-name:var(--font-geist-mono)]
  return (
    <section className="container mx-auto py-12 px-4 relative">
      <div className="max-w-3xl mx-auto text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold mb-8">
          <span className="text-primary">Explore MCP Servers</span>
        </h2>
        <p className="text-xl text-foreground">
          Browse our complete collection of Model Context Protocol servers to find one that meets
          your specific needs, or{" "}
          <Link href="/chat" className="text-primary">
            talk to our AI expert
          </Link>{" "}
          to discover new ones.
        </p>
      </div>
      <MCPServerList />
    </section>
  );
}
