import MCPServerDetails from "@/components/mcp-server-details";
import { CommentSection } from "@/components/comment-section";
import { IntegrationGuide } from "@/components/integration-guide";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { getMcpServer } from "@/lib/data";
import AboutSection from "@/components/about-section";
import Link from "next/link";

export default async function McpServerPage({ params }: { params: { id: string } }) {
  const { id } = await params; // https://nextjs.org/docs/messages/sync-dynamic-apis
  const server = getMcpServer(id);

  if (!server) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-4xl font-bold mb-4">Server Not Found</h1>
        <p className="text-xl">
          The MCP server you&apos;re looking for doesn&apos;t exist or has been removed.
        </p>
      </div>
    );
  }

  return (
    <div className="container relative mx-auto px-4 py-16">
      <Link href="/marketplace" className="absolute top-3 right-0 text-primary hover:underline">
        Back to Marketplace
      </Link>
      <MCPServerDetails server={server} />

      <div className="mt-12">
        <Tabs defaultValue="about" className="w-full">
          <TabsList className="grid w-full grid-cols-3 border border-input shadow-lg backdrop-blur-sm">
            <TabsTrigger
              value="about"
              className="cursor-pointer data-[state=active]:text-white data-[state=active]:bg-primary hover:data-[state=inactive]:text-primary dark:data-[state=active]:bg-primary transition-colors"
            >
              About
            </TabsTrigger>
            <TabsTrigger
              value="integration"
              className="cursor-pointer data-[state=active]:text-white data-[state=active]:bg-primary hover:data-[state=inactive]:text-primary dark:data-[state=active]:bg-primary transition-colors"
            >
              Integration Guide
            </TabsTrigger>
            <TabsTrigger
              value="comments"
              className="cursor-pointer data-[state=active]:text-white data-[state=active]:bg-primary hover:data-[state=inactive]:text-primary dark:data-[state=active]:bg-primary transition-colors"
            >
              Comments
            </TabsTrigger>
          </TabsList>
          <TabsContent
            value="about"
            className="mt-6 border border-input rounded-2xl shadow-lg backdrop-blur-sm"
          >
            <AboutSection server={server} />
          </TabsContent>
          <TabsContent
            value="integration"
            className="mt-6 border border-input rounded-2xl shadow-lg backdrop-blur-sm"
          >
            <IntegrationGuide server={server} />
          </TabsContent>
          <TabsContent
            value="comments"
            className="mt-6 border border-input rounded-2xl shadow-lg backdrop-blur-sm"
          >
            <CommentSection server_id={id} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
