import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";
import { ThemeProvider } from "@/lib/providers/theme-provider";
import { checkUser } from "@/lib/supabase-server-actions";
import { GlobalContextProvider } from "@/lib/providers/providers";

const geist_sans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geist_mono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "MCP Marketplace",
  description: "Use AI to find the Perfect MCP Server for your needs.",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const user = await checkUser();

  return (
    <html lang="en" suppressHydrationWarning={true}>
      <body className={`${geist_sans.variable} ${geist_mono.variable} antialiased`}>
        <ThemeProvider
          attribute="class"
          themes={["dark", "light", "system"]}
          defaultTheme="system"
          enableSystem={true}
        >
          <GlobalContextProvider user={user}>{children}</GlobalContextProvider>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
