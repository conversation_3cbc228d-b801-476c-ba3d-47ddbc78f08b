// Chat Activity Tracker - TypeScript Version

import { ActiveChat } from "./types";

// Default timeout in minutes
const DEFAULT_TIMEOUT_MINUTES = 30;

// Session management utilities
export const activeChatSession = {
  /**
   * Update the last active timestamp for a chat
   */
  update: (key: string, chatId: string): void => {
    if (typeof window === "undefined" || !("localStorage" in window)) return;

    const sessionData: ActiveChat = {
      chat_id: chatId,
      last_active: Date.now(),
    };

    try {
      localStorage.setItem(key, JSON.stringify(sessionData));
    } catch (error) {
      console.error("Error updating chat session:", error);
    }
  },

  /**
   * Get the current active chat ID if session is still valid
   */
  getChatId: (key: string, timeoutMinutes: number = DEFAULT_TIMEOUT_MINUTES): string | null => {
    if (typeof window === "undefined" || !("localStorage" in window)) return null;

    try {
      const sessionData = localStorage.getItem(key);
      if (!sessionData) return null;

      const parsed: ActiveChat = JSON.parse(sessionData);

      // Validate structure
      if (typeof parsed.chat_id !== "string" || typeof parsed.last_active !== "number") {
        activeChatSession.clear(key);
        return null;
      }

      // Check if session is still active
      const timeoutMs = timeoutMinutes * 60 * 1000;
      const isActive = Date.now() - parsed.last_active < timeoutMs;

      // If not active, clear and return null
      if (!isActive) {
        activeChatSession.clear(key);
        return null;
      }

      return parsed.chat_id;
    } catch (error) {
      console.error("Error getting chat session:", error);
      activeChatSession.clear(key);
      return null;
    }
  },

  /**
   * Check if there's an active session without returning the ID
   */
  isActive: (key: string, timeoutMinutes: number = DEFAULT_TIMEOUT_MINUTES): boolean => {
    return activeChatSession.getChatId(key, timeoutMinutes) !== null;
  },

  /**
   * Get full session data
   */
  getData: (key: string): ActiveChat | null => {
    if (typeof window === "undefined" || !("localStorage" in window)) return null;

    try {
      const sessionData = localStorage.getItem(key);
      if (!sessionData) return null;

      const parsed: ActiveChat = JSON.parse(sessionData);

      // Validate structure
      if (typeof parsed.chat_id !== "string" || typeof parsed.last_active !== "number") {
        activeChatSession.clear(key);
        return null;
      }

      return parsed;
    } catch (error) {
      console.error("Error getting session data:", error);
      return null;
    }
  },

  /**
   * Clear the active session
   */
  clear: (key: string): void => {
    if (typeof window === "undefined" || !("localStorage" in window)) return;

    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error("Error clearing chat session:", error);
    }
  },
};
