import { reviveChatDates } from "@/utils/utils";

// Save data to localStorage
export const saveToLocalStorage = <T>(key: string, value: T): void => {
  try {
    const serializedValue = JSON.stringify(value);
    localStorage.setItem(key, serializedValue);
  } catch (error) {
    console.error("Error saving to localStorage:", error);
  }
};

// Read data from localStorage
// function reviveVisitorChat(raw: unknown): VisitorChat {
//   return {
//     chat_id: raw.chat_id,
//     created_at: new Date(raw.created_at),
//     updated_at: new Date(raw.updated_at),
//     messages: raw.messages.map((m: Message) => ({
//       ...m,
//       createdAt: m.createdAt ? new Date(m.createdAt) : undefined,
//     })),
//   };
// }

export const readFromLocalStorage = <T>(key: string): T | null => {
  try {
    const item = localStorage.getItem(key);
    if (item === null) {
      return null;
    }
    return JSON.parse(item);
  } catch (error) {
    console.error("Error reading from localStorage:", error);
    return null;
  }
};

export const readChatsFromLocalStorage = <T>(key: string): T | null => {
  try {
    const item = localStorage.getItem(key);
    if (item === null) {
      return null;
    }
    const parsed = JSON.parse(item);
    return reviveChatDates(parsed);
  } catch (error) {
    console.error("Error reading from localStorage:", error);
    return null;
  }
};

// Remove item from localStorage
export const removeFromLocalStorage = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error("Error removing from localStorage:", error);
  }
};

// Check if localStorage is available (useful for SSR)
export const isLocalStorageAvailable = (): boolean => {
  try {
    return typeof window !== "undefined" && "localStorage" in window;
  } catch {
    return false;
  }
};
