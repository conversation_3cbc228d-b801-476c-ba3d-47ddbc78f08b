import { activeChatSession } from "./active-chat-session";

interface ActivityTrackingConfig {
  throttleMs?: number;
  selectors?: {
    container?: string;
    window?: string;
    input?: string;
  };
}

let activityCleanup: (() => void) | null = null;
let lastUpdateTime = 0;

/**
 * Throttled function to update chat activity
 */
const createThrottledUpdate = (key: string, chatId: string, throttleMs: number = 500) => {
  return () => {
    const now = Date.now();
    if (now - lastUpdateTime >= throttleMs) {
      lastUpdateTime = now;
      activeChatSession.update(key, chatId);
    }
  };
};

/**
 * Add event listener with cleanup tracking
 */
const addTrackedListener = (
  element: Element | Window,
  event: string,
  handler: EventListener,
  options?: AddEventListenerOptions,
): (() => void) => {
  element.addEventListener(event, handler, options);
  return () => element.removeEventListener(event, handler);
};

export const activityTracking = {
  /**
   * Start tracking user activity for a chat
   * Returns a cleanup function to stop tracking
   */
  start: (key: string, chatId: string, config: ActivityTrackingConfig = {}): (() => void) => {
    if (typeof window === "undefined") {
      return () => {}; // Return no-op cleanup for SSR
    }

    // Stop any existing tracking
    activityTracking.stop();

    const {
      throttleMs = 500,
      selectors = {
        container: null,
        window: "[data-chat-window]",
        input: "[data-chat-input]",
      },
    } = config;

    const throttledUpdate = createThrottledUpdate(key, chatId, throttleMs);
    const cleanupFunctions: (() => void)[] = [];

    // Get elements
    const container = document.querySelector(selectors.container!) as HTMLElement;
    const chatWindow = document.querySelector(selectors.window!) as HTMLElement;
    const chatInput = document.querySelector(selectors.input!) as HTMLInputElement;

    // Track input interactions
    if (chatInput) {
      cleanupFunctions.push(
        addTrackedListener(chatInput, "input", throttledUpdate),
        addTrackedListener(chatInput, "focus", throttledUpdate),
        addTrackedListener(chatInput, "click", throttledUpdate),
      );
    }

    // Track chat window interactions
    if (chatWindow) {
      cleanupFunctions.push(
        addTrackedListener(chatWindow, "scroll", throttledUpdate),
        addTrackedListener(chatWindow, "click", throttledUpdate),
      );
    }

    // Track container interactions
    if (container) {
      cleanupFunctions.push(
        addTrackedListener(container, "mouseenter", throttledUpdate),
        addTrackedListener(container, "focus", throttledUpdate, { capture: true }),
      );
    }

    // Track window focus
    cleanupFunctions.push(addTrackedListener(window, "focus", throttledUpdate));

    // Create master cleanup function
    const cleanup = () => {
      cleanupFunctions.forEach((fn) => {
        try {
          fn();
        } catch (error) {
          console.error("Error during activity tracking cleanup:", error);
        }
      });
      activityCleanup = null;
    };

    activityCleanup = cleanup;

    // Update immediately when starting
    activeChatSession.update(key, chatId);

    return cleanup;
  },

  /**
   * Stop all activity tracking
   */
  stop: (): void => {
    if (activityCleanup) {
      activityCleanup();
      activityCleanup = null;
    }
  },

  /**
   * Check if activity tracking is currently running
   */
  isActive: (): boolean => {
    return activityCleanup !== null;
  },
};
