"use server";

import z from "zod";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import { RegistrationSchema, LoginSchema } from "./schemas/auth-form-schemas";

export async function register(
  form_data: z.infer<typeof RegistrationSchema>,
  captchaToken: string,
) {
  const supabase = await createClient();

  const { error } = await supabase.auth.signUp({
    email: form_data.email,
    password: form_data.password,
    options: {
      data: {
        display_name: form_data.name,
      },
      captchaToken,
    },
  });

  if (error) {
    redirect("/error");
  }

  revalidatePath("/", "layout");
  redirect(
    `/auth/confirmation?message=Please check your email (${form_data.email}) to continue the sign-up process`,
  );
}

export async function login(form_data: z.infer<typeof LoginSchema>) {
  const supabase = await createClient();

  const { error } = await supabase.auth.signInWithPassword({
    email: form_data.email,
    password: form_data.password,
  });

  if (error) {
    redirect("/error");
  }

  revalidatePath("/", "layout");
  redirect("/");
}

export async function logOut() {
  const supabase = await createClient();

  const { error } = await supabase.auth.signOut();

  if (error) {
    redirect("/error");
  }

  revalidatePath("/");
  redirect("/");
}

// Checking if current user is logged in before granting permission
export const checkUser = async () => {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return null;
  }

  return user;
};
