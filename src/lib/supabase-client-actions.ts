"use client";
import { Provider } from "@supabase/supabase-js";
import { createClient } from "@/utils/supabase/client";

// Handle Reset password link from email
export const resetPassword = async (form_data: FormData) => {
  const supabase = createClient();
  const newPassword = form_data.get("password") as string;

  const { error } = await supabase.auth.updateUser({
    password: newPassword,
  });

  if (error) {
    return { error: error.message };
  }

  return { success: true };
};

// OAuth sign-in with PKCE flow
export const oAuthSignIn = async (provider: Provider, origin: string) => {
  const supabase = createClient();
  await supabase.auth.signInWithOAuth({
    provider,
    options: {
      redirectTo: `${origin}/auth/callback`,
    },
  });
};
