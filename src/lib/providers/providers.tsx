import { ReactNode } from "react";
import { User } from "@supabase/supabase-js";
import { UserProvider } from "@/lib/providers/user-provider";
import { ChatToggleProvider } from "@/lib/providers/chat-toggle-provider";
// import { PhoneProvider } from "@/lib/contexts/phone-context";
// import { RefreshCacheProvider } from "@/lib/contexts/refresh-cache-context";
// import { ChatsProvider } from "@/lib/contexts/chat-context";
// import { refreshCache } from "@/utils/server-utils";
// import { Chat } from "../types";

interface Props {
  children: ReactNode;
  user: User | null;
  // chats: Chat[] | null;
  // hasPhone: boolean;
}

export const GlobalContextProvider = ({ children, user }: Props) => {
  return (
    <UserProvider user={user}>
      <ChatToggleProvider>
        {/* <ChatsProvider chats={chats}> */}
        {/* <PhoneProvider hasPhone={hasPhone}> */}
        {/* <RefreshCacheProvider refreshCache={refreshCache}> */}
        {children}
        {/* </RefreshCacheProvider> */}
        {/* </PhoneProvider> */}
        {/* </ChatsProvider> */}
      </ChatToggleProvider>
    </UserProvider>
  );
};
