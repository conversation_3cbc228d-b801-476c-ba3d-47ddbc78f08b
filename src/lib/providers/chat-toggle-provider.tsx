"use client";
import { createContext, ReactNode, useContext, useState } from "react";

interface ChatToggleContextType {
  isClosed: boolean;
  setIsClosed: (isClosed: boolean) => void;
}

const ChatToggleContext = createContext<ChatToggleContextType | undefined>(undefined);

export const useChatToggle = () => {
  const context = useContext(ChatToggleContext);
  if (context === undefined) {
    throw new Error("useChatToggle must be used within a ChatToggleProvider");
  }
  return context;
};

interface ProviderProps {
  children: ReactNode;
}

export const ChatToggleProvider = ({ children }: ProviderProps) => {
  const [isClosed, setIsClosed] = useState(false);

  return (
    <ChatToggleContext.Provider value={{ isClosed, setIsClosed }}>
      {children}
    </ChatToggleContext.Provider>
  );
};
