"use client";
import { User } from "@supabase/supabase-js";
import { createContext, ReactNode, useContext } from "react";

interface ContextTypes {
  user: User | null;
}

const UserContext = createContext<ContextTypes | undefined>(undefined);

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};

interface ProviderProps {
  children: ReactNode;
  user: User | null;
}

export const UserProvider = ({ children, user }: ProviderProps) => {
  return <UserContext.Provider value={{ user }}>{children}</UserContext.Provider>;
};
