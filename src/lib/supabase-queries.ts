"use server";

import { createClient, PostgrestResponse } from "@supabase/supabase-js";
import { Chat } from "./types";
import { UUID } from "crypto";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { Message } from "ai";
import { reviveChatDates } from "@/utils/utils";

const dataBase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_SERVICE_KEY!);

export async function getChat(chat_id: string, user_id: string | UUID): Promise<Chat | null> {
  const { data, error }: PostgrestResponse<Chat> = await dataBase
    .from("chats")
    .select("*")
    .eq("user_id", user_id)
    .eq("chat_id", chat_id);

  if (data) {
    if (data.length) {
      const chat = reviveChatDates(data[0]);
      return chat;
    }
    return data[0];
  }

  if (error) {
    console.error(error);
  }

  return null;
}

export async function getAllChats(user_id: string | UUID): Promise<Chat[]> {
  const { data, error }: PostgrestResponse<Chat> = await dataBase
    .from("chats")
    .select("*")
    .eq("user_id", user_id);

  if (data) {
    return data;
  }

  if (error) {
    console.error(error);
  }

  return [];
}

export async function saveNewChat(chat: Chat) {
  const { error } = await dataBase.from("chats").insert(chat);
  if (error) {
    console.error(error);
  }
}

export async function updateChat(user_id: string | UUID, chat_id: string, messages: Message[]) {
  const updated_at = messages[messages.length - 1].createdAt;
  const { error } = await dataBase
    .from("chats")
    .update({
      messages: messages,
      updated_at: updated_at,
    })
    .eq("user_id", user_id)
    .eq("chat_id", chat_id);

  if (error) {
    console.error(error);
  }
}

export async function deleteChat(chat_id: string, user_id: string | UUID) {
  const { error } = await dataBase
    .from("chats")
    .delete()
    .eq("user_id", user_id)
    .eq("chat_id", chat_id);

  if (error) {
    return { error: error.message };
  }

  return revalidatePath("/chat");
}

export async function deleteAllChats(user_id: string | UUID) {
  const { error } = await dataBase.from("chats").delete().in("user_id", [user_id]);

  if (error) {
    return { error: error.message };
  }

  revalidatePath("/chat");
  return redirect("/chat");
}
