"use client";
import { z } from "zod";

export const RegistrationSchema = z
  .object({
    name: z.string().min(2, { message: "Name must be at-least 2 characters long" }).trim(),
    email: z.string().email({ message: "please enter a valid email" }).trim(),
    password: z.string().min(8, { message: "Password must be at least 8 characters long" }).trim(),
    confirmPassword: z.string().trim(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"], // This will attach the error to the confirmPassword field
  });

export const LoginSchema = z.object({
  email: z.string().email({ message: "please enter a valid email" }).trim(),
  password: z.string().min(8, { message: "Password must be at least 8 characters long" }).trim(),
});

export const ResetPasswordSchema = z.object({
  password: z.string().min(8, { message: "Be at least 8 characters long" }).trim(),
});
