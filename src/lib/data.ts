import type { ServerType, CommentType, UserType } from "./types";

// Mock data for MCP servers
const mcpServers: ServerType[] = [
  {
    id: "server1",
    name: "QuantumMCP",
    description:
      "High-performance MCP server with advanced quantum computing capabilities for real-time control systems.",
    fullDescription:
      "QuantumMCP is a cutting-edge Model Context Protocol server designed for high-performance industrial applications. Leveraging quantum computing principles, it offers unprecedented speed and reliability for real-time control systems. The server supports both traditional and next-generation protocols, making it ideal for organizations transitioning to Industry 4.0 standards.",
    logo: "/placeholder.svg?height=200&width=200",
    rating: 4.5,
    reviewCount: 124,
    version: "3.2.1",
    tags: ["Quantum", "High-Performance", "Industrial", "Real-time"],
    features: [
      "Quantum-inspired optimization algorithms",
      "Sub-millisecond response times",
      "Fault-tolerant architecture",
      "Advanced security features",
      "Comprehensive logging and monitoring",
      "Support for legacy protocols",
    ],
    githubUrl: "https://github.com/example/quantummcp",
    websiteUrl: "https://quantummcp.example.com",
    lastUpdated: "2023-11-15",
    userCount: 5280,
    license: "Commercial",
    protocol: "MCP v4.5",
  },
  {
    id: "server2",
    name: "NexusMCP",
    description:
      "Open-source MCP server with extensive plugin support and a vibrant community of developers.",
    fullDescription:
      "NexusMCP is a community-driven, open-source Model Context Protocol server that emphasizes flexibility and extensibility. With its robust plugin architecture, developers can easily customize and extend functionality to meet specific requirements. The server is backed by a vibrant community that continuously contributes improvements and new features.",
    rating: 4,
    reviewCount: 89,
    version: "2.8.0",
    tags: ["Open-Source", "Community", "Plugins", "Flexible"],
    features: [
      "Modular plugin architecture",
      "Cross-platform compatibility",
      "Extensive documentation",
      "Active community support",
      "Regular security updates",
      "Custom protocol extensions",
    ],
    githubUrl: "https://github.com/example/nexusmcp",
    websiteUrl: "https://nexusmcp.example.org",
    lastUpdated: "2023-12-05",
    userCount: 8750,
    license: "MIT",
    protocol: "MCP v3.8",
  },
  {
    id: "server3",
    name: "CloudMCP",
    description:
      "Cloud-native MCP server designed for scalability and seamless integration with major cloud providers.",
    fullDescription:
      "CloudMCP is a cloud-native Model Context Protocol server built from the ground up for modern distributed systems. It offers seamless integration with all major cloud providers and containerization technologies. With its auto-scaling capabilities and microservices architecture, CloudMCP can handle varying workloads efficiently while maintaining high availability.",
    logo: "/placeholder.svg?height=200&width=200",
    rating: 4,
    reviewCount: 67,
    version: "1.5.3",
    tags: ["Cloud", "Scalable", "Kubernetes", "Microservices"],
    features: [
      "Native Kubernetes integration",
      "Auto-scaling capabilities",
      "Multi-cloud support",
      "Serverless deployment options",
      "Built-in monitoring and alerting",
      "Disaster recovery features",
    ],
    githubUrl: "https://github.com/example/cloudmcp",
    websiteUrl: "https://cloudmcp.example.io",
    lastUpdated: "2024-01-10",
    userCount: 3120,
    license: "Apache 2.0",
    protocol: "MCP v4.0",
  },
  {
    id: "server4",
    name: "SecureMCP",
    description:
      "Security-focused MCP server with advanced encryption, authentication, and compliance features.",
    fullDescription:
      "SecureMCP is a security-first Model Context Protocol server designed for organizations with stringent compliance requirements. It implements state-of-the-art encryption, multi-factor authentication, and comprehensive audit logging. The server is regularly audited by third-party security firms and maintains compliance with major industry standards.",
    rating: 2,
    reviewCount: 52,
    version: "2.3.4",
    tags: ["Security", "Compliance", "Enterprise", "Encryption"],
    features: [
      "End-to-end encryption",
      "Multi-factor authentication",
      "Role-based access control",
      "Comprehensive audit logging",
      "Compliance with GDPR, HIPAA, and ISO 27001",
      "Intrusion detection and prevention",
    ],
    githubUrl: "https://github.com/example/securemcp",
    websiteUrl: "https://securemcp.example.com",
    lastUpdated: "2023-10-22",
    userCount: 1890,
    license: "Commercial",
    protocol: "MCP v4.2",
  },
  {
    id: "server5",
    name: "EdgeMCP",
    description:
      "Lightweight MCP server optimized for edge computing and IoT deployments with minimal resource usage.",
    fullDescription:
      "EdgeMCP is a lightweight Model Context Protocol server specifically optimized for edge computing and IoT deployments. With its minimal resource footprint, it can run efficiently on constrained hardware while still providing reliable protocol handling. The server includes specialized features for managing intermittent connectivity and synchronizing with cloud backends.",
    logo: "/placeholder.svg?height=200&width=200",
    rating: 5,
    reviewCount: 41,
    version: "1.2.0",
    tags: ["Edge", "IoT", "Lightweight", "Embedded"],
    features: [
      "Minimal resource footprint",
      "Offline operation capabilities",
      "Efficient data synchronization",
      "Power-aware scheduling",
      "Support for constrained networks",
      "Device management features",
    ],
    githubUrl: "https://github.com/example/edgemcp",
    websiteUrl: "https://edgemcp.example.net",
    lastUpdated: "2023-09-18",
    userCount: 4560,
    license: "BSD",
    protocol: "MCP v3.5",
  },
  {
    id: "server6",
    name: "RapidMCP",
    description:
      "Ultra-fast MCP server with optimized performance for high-throughput applications and time-critical systems.",
    fullDescription:
      "RapidMCP is an ultra-fast Model Context Protocol server engineered for applications where every microsecond counts. Through extensive optimization and innovative architecture, it achieves unprecedented throughput and minimal latency. The server is particularly well-suited for high-frequency trading, real-time analytics, and other time-critical systems.",
    rating: 3,
    reviewCount: 38,
    version: "4.0.1",
    tags: ["High-Speed", "Low-Latency", "Performance", "Time-Critical"],
    features: [
      "Sub-microsecond latency",
      "Memory-optimized data structures",
      "Lock-free concurrency",
      "Hardware acceleration support",
      "Predictable performance characteristics",
      "Specialized time synchronization",
    ],
    githubUrl: "https://github.com/example/rapidmcp",
    websiteUrl: "https://rapidmcp.example.com",
    lastUpdated: "2024-02-05",
    userCount: 980,
    license: "Commercial",
    protocol: "MCP v5.0",
  },
];

// Mock users
const users: UserType[] = [
  {
    id: "user1",
    name: "Alex Johnson",
    avatar: "/placeholder.svg?height=40&width=40",
    email: "<EMAIL>",
  },
  {
    id: "user2",
    name: "Samantha Lee",
    avatar: "/placeholder.svg?height=40&width=40",
    email: "<EMAIL>",
  },
  {
    id: "user3",
    name: "David Chen",
    avatar: "/placeholder.svg?height=40&width=40",
    email: "<EMAIL>",
  },
  {
    id: "user4",
    name: "Maria Rodriguez",
    avatar: "/placeholder.svg?height=40&width=40",
    email: "<EMAIL>",
  },
];

// Mock comments
const comments: CommentType[] = [
  {
    id: "comment1",
    serverId: "server1",
    user: users[0],
    content:
      "We've been using QuantumMCP for our manufacturing line control systems for the past six months, and the performance has been exceptional. The sub-millisecond response times have significantly improved our production efficiency.",
    rating: 5,
    date: "2023-12-15",
  },
  {
    id: "comment2",
    serverId: "server1",
    user: users[1],
    content:
      "The quantum-inspired optimization algorithms are impressive, but the learning curve is steep. Documentation could be improved, especially for advanced features.",
    rating: 4,
    date: "2024-01-03",
  },
  {
    id: "comment3",
    serverId: "server2",
    user: users[2],
    content:
      "As an open-source enthusiast, I love NexusMCP's community-driven approach. The plugin ecosystem is rich, and I've been able to customize it perfectly for our needs.",
    rating: 5,
    date: "2023-11-22",
  },
  {
    id: "comment4",
    serverId: "server2",
    user: users[3],
    content:
      "Great server overall, but I've encountered some stability issues with the latest version when running with more than 20 plugins simultaneously.",
    rating: 3,
    date: "2024-02-10",
  },
  {
    id: "comment5",
    serverId: "server3",
    user: users[0],
    content:
      "CloudMCP's integration with our AWS infrastructure was seamless. The auto-scaling capabilities have saved us significant operational costs during peak usage periods.",
    rating: 4,
    date: "2023-10-18",
  },
];

// Helper functions to retrieve data
export function getMcpServers(): ServerType[] {
  return mcpServers;
}

export function getMcpServer(id: string): ServerType | undefined {
  return mcpServers.find((server) => server.id === id);
}

export function getComments(server_id: string): CommentType[] {
  return comments.filter((comment) => comment.serverId === server_id);
}

export function getUser(id: string): UserType | undefined {
  return users.find((user) => user.id === id);
}
