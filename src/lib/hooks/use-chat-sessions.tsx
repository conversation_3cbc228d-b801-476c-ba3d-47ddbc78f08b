import { useEffect, useState, useCallback, useRef } from "react";
import { activeChatSession } from "../active-chat-session";
import { activityTracking } from "../chat-activity-tracking";

interface UseChatSessionConfig {
  key: string;
  sessionTimeoutMinutes?: number;
  activityThrottleMs?: number;
  autoStart?: boolean;
}

interface UseChatSessionReturn {
  activeChatId: string | null;
  isSessionActive: boolean;
  isTrackingActivity: boolean;
  startChatTracking: (chat_id: string) => void;
  stopChatTracking: () => void;
  clearSession: () => void;
  updateActivity: () => void;
}

export function useChatSession(
  config: UseChatSessionConfig = {
    key: "",
  },
): UseChatSessionReturn {
  const { key, sessionTimeoutMinutes = 30, activityThrottleMs = 500, autoStart = true } = config;

  const [activeChatId, setActiveChatId] = useState<string | null>(null);
  const [isSessionActive, setIsSessionActive] = useState(false);
  const [isTrackingActivity, setIsTrackingActivity] = useState(false);
  const cleanupRef = useRef<(() => void) | null>(null);

  const startActivityTracking = useCallback(
    (chat_id: string) => {
      // Stop existing tracking
      if (cleanupRef.current) {
        cleanupRef.current();
        cleanupRef.current = null;
        setIsTrackingActivity(false);
      }

      // Wait for DOM elements to be available
      const startTracking = () => {
        const cleanup = activityTracking.start(key, chat_id, {
          throttleMs: activityThrottleMs,
        });

        cleanupRef.current = cleanup;
        setIsTrackingActivity(true);
      };

      // Try immediately, then with delay if elements not found
      if (document.querySelector("[data-chat-container]")) {
        startTracking();
      } else {
        setTimeout(startTracking, 500);
      }
    },
    [activityThrottleMs, key],
  );

  // Check for existing session on mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      const existingChatId = activeChatSession.getChatId(key, sessionTimeoutMinutes);
      if (existingChatId) {
        setActiveChatId(existingChatId);
        setIsSessionActive(true);

        if (autoStart) {
          setTimeout(() => startActivityTracking(existingChatId), 500);
        }
      }
    }
  }, [sessionTimeoutMinutes, autoStart, key, startActivityTracking]);

  const startChatTracking = useCallback(
    (chat_id: string) => {
      activeChatSession.update(key, chat_id);
      setActiveChatId(chat_id);
      setIsSessionActive(true);
      startActivityTracking(chat_id);
    },
    [key, startActivityTracking],
  );

  const stopChatTracking = useCallback(() => {
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = null;
    }
    setIsTrackingActivity(false);
  }, []);

  const clearSession = useCallback(() => {
    stopChatTracking();
    activeChatSession.clear(key);
    setActiveChatId(null);
    setIsSessionActive(false);
  }, [key, stopChatTracking]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
      }
    };
  }, []);

  const updateActivity = useCallback(() => {
    if (activeChatId && key) {
      activeChatSession.update(key, activeChatId);
    }
  }, [activeChatId, key]);

  return {
    activeChatId,
    isSessionActive,
    isTrackingActivity,
    startChatTracking,
    stopChatTracking,
    clearSession,
    updateActivity,
  };
}
