import { Message } from "ai";
import { UUID } from "crypto";

export interface ServerType {
  id: string;
  name: string;
  description: string;
  fullDescription: string;
  logo?: string;
  rating: number;
  reviewCount: number;
  version: string;
  tags: string[];
  features: string[];
  githubUrl?: string;
  websiteUrl?: string;
  lastUpdated: string;
  userCount: number;
  license: string;
  protocol: string;
}

export interface UserType {
  id: string;
  name: string;
  avatar?: string;
  email: string;
}

export interface CommentType {
  id: string;
  serverId: string;
  user: UserType;
  content: string;
  rating: number;
  date: string;
}

export type FormState =
  | {
      errors?: {
        name?: string[];
        email?: string[];
        password?: string[];
      };
      message?: string;
    }
  | undefined;

export interface Chat extends Record<string, unknown> {
  user_id: UUID | string;
  chat_id: string;
  title: string;
  messages: Message[];
  created_at: Date;
  updated_at: Date;
  path: string;
  share_path?: string;
  has_live_link?: boolean;
  share_id?: string;
  shared_at?: Date;
  static_link_count: number;
}

export interface VisitorChat extends Record<string, unknown> {
  chat_id: string;
  messages: Message[];
  created_at: Date;
  updated_at: Date;
}
export interface ActiveChat extends Record<string, unknown> {
  chat_id: string;
  last_active: number;
}
