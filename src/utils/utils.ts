import { openai } from "@ai-sdk/openai";
import { generateText, Message } from "ai";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const ratingLabels: { [index: string]: string } = {
  0.5: "Useless", //0 - 0.5
  1: "Useless+", //0.6 - 1
  1.5: "Poor", // 1.1 - 1.5
  2: "Poor+", // 1.6 - 2
  2.5: "Ok", // 2.1 - 2.5
  3: "Ok+", // 2.6 - 3
  3.5: "Good", // 3.1 - 3.5
  4: "Good+", // 3.6 - 4
  4.5: "Excellent", // 4.1 - 4.5
  5: "Excellent+", // 4.6 - 5
};

export function roundUpToNearestHalf(value: number): number {
  return Math.ceil(value * 2) / 2;
}

export const getInitials = (name: string) => {
  if (name) {
    const names = name.trim().split(/\s+/);

    if (names.length === 1) {
      return names[0].slice(0, 2).toUpperCase();
    } else if (names.length >= 2) {
      return (names[0][0] + names[names.length - 1][0]).toUpperCase();
    }
  }
  return "";
};

export const getName = (name: string) => {
  if (name) {
    const names = name.trim().split(/\s+/);

    if (names.length === 1) {
      return names[0];
    } else if (names.length >= 2) {
      return names[0];
    }
  }
  return "";
};

export const formatDate = (input: string | number | Date): string => {
  const date = new Date(input);
  return date.toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric",
  });
};
export const formatDateAndTime = (input: string | number | Date): string => {
  const date = new Date(input);
  return date.toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric",
    hour: "numeric",
    minute: "numeric",
    hour12: true, // Use 12-hour format (optional, set to false for 24-hour format)
  });
};

export function reviveChatDates<
  ChatType extends { created_at: Date; updated_at: Date; messages: Message[] },
>(raw: ChatType): ChatType {
  // 1. Top‐level fields:
  if (typeof raw.created_at === "string") {
    const d = new Date(raw.created_at);
    if (!isNaN(d.getTime())) raw.created_at = d;
  }
  if (typeof raw.updated_at === "string") {
    const d = new Date(raw.updated_at);
    if (!isNaN(d.getTime())) raw.updated_at = d;
  }

  // 2. Each message inside `messages[]`:
  raw.messages = raw.messages.map((m: Message) => {
    if (m.createdAt && typeof m.createdAt === "string") {
      const dm = new Date(m.createdAt);
      if (!isNaN(dm.getTime())) {
        return { ...m, createdAt: dm };
      }
    }
    return m;
  });

  return raw;
}
export async function generateTitle(message: Message) {
  const { text: title } = await generateText({
    model: openai("gpt-4.1-nano"),
    system: `\n
    - you will generate a short title based on the first message a user begins a conversation with
    - ensure it is not more than 80 characters long
    - the title should be a summary of the user's message`,
    // - do not use quotes or colons`
    prompt: JSON.stringify(message),
  });
  return title;
}
