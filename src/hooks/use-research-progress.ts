import { useState, useEffect } from 'react';
import { ResearchStep } from '@/lib/types';

interface UseResearchProgressProps {
  messages: any[];
  status: string;
  deepResearch: boolean;
}

export function useResearchProgress({ messages, status, deepResearch }: UseResearchProgressProps) {
  const [currentStep, setCurrentStep] = useState<ResearchStep | null>(null);
  const [completedSteps, setCompletedSteps] = useState<ResearchStep[]>([]);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    if (!deepResearch) {
      setCurrentStep(null);
      setCompletedSteps([]);
      setIsComplete(false);
      return;
    }

    // Get the latest assistant message
    const latestMessage = messages.filter(m => m.role === 'assistant').pop();
    if (!latestMessage) return;

    const content = latestMessage.content || '';
    
    // Parse research steps from content
    const researchPatterns = [
      { 
        pattern: /🚀\s*Starting MCP Server Research/i, 
        tool: "web_search", 
        title: "Starting research", 
        description: "Initiating comprehensive search" 
      },
      { 
        pattern: /🔍\s*Web Search/i, 
        tool: "web_search", 
        title: "Web search", 
        description: "Searching for relevant information" 
      },
      { 
        pattern: /📄\s*Analyzing Webpage/i, 
        tool: "visit_page", 
        title: "Analyzing webpage", 
        description: "Extracting information from sources" 
      },
      { 
        pattern: /📖\s*Navigating Content/i, 
        tool: "page_navigation", 
        title: "Navigating content", 
        description: "Exploring page content" 
      },
      { 
        pattern: /🔎\s*Searching Within Page/i, 
        tool: "find_on_page", 
        title: "Searching within page", 
        description: "Finding specific information" 
      },
      { 
        pattern: /✅\s*Research Complete/i, 
        tool: "reasoning_tool", 
        title: "Research complete", 
        description: "Finalizing analysis",
        complete: true 
      },
    ];

    const foundSteps: ResearchStep[] = [];
    let currentActiveStep: ResearchStep | null = null;
    let researchComplete = false;

    // Find all research indicators in the content
    for (const { pattern, tool, title, description, complete } of researchPatterns) {
      if (pattern.test(content)) {
        const step: ResearchStep = {
          id: `step-${tool}-${Date.now()}`,
          tool,
          title,
          description,
          status: complete ? "completed" : "completed",
          timestamp: new Date()
        };

        if (complete) {
          researchComplete = true;
          foundSteps.push(step);
        } else {
          foundSteps.push(step);
        }
      }
    }

    // If chat is still in progress and we have steps, the last one is active
    if (status === "submitted" && foundSteps.length > 0 && !researchComplete) {
      const lastStep = foundSteps.pop();
      if (lastStep) {
        currentActiveStep = { ...lastStep, status: "active" };
      }
    }

    // Update state
    setCompletedSteps(foundSteps);
    setCurrentStep(currentActiveStep);
    setIsComplete(researchComplete);

    // Auto-hide after completion
    if (researchComplete && status === "ready") {
      setTimeout(() => {
        setCurrentStep(null);
        setCompletedSteps([]);
        setIsComplete(false);
      }, 3000);
    }

  }, [messages, status, deepResearch]);

  // Reset when starting new research
  useEffect(() => {
    if (status === "submitted" && deepResearch) {
      setCurrentStep(null);
      setCompletedSteps([]);
      setIsComplete(false);
    }
  }, [status, deepResearch]);

  return {
    currentStep,
    completedSteps,
    isComplete
  };
}

// Helper function to filter research progress from message content
export function filterResearchProgress(content: string): string {
  const researchSections = [
    /🚀\s*Starting MCP Server Research[\s\S]*?(?=\n\n[A-Z]|$)/gi,
    /🔍\s*Web Search[\s\S]*?(?=\n\n[A-Z]|$)/gi,
    /📄\s*Analyzing Webpage[\s\S]*?(?=\n\n[A-Z]|$)/gi,
    /📖\s*Navigating Content[\s\S]*?(?=\n\n[A-Z]|$)/gi,
    /🔎\s*Searching Within Page[\s\S]*?(?=\n\n[A-Z]|$)/gi,
    /✅\s*Research Complete[\s\S]*?(?=\n\n[A-Z]|$)/gi,
  ];

  let filteredContent = content;
  
  for (const pattern of researchSections) {
    filteredContent = filteredContent.replace(pattern, '');
  }

  // Clean up extra whitespace
  filteredContent = filteredContent
    .replace(/\n{3,}/g, '\n\n')
    .trim();

  return filteredContent;
}
