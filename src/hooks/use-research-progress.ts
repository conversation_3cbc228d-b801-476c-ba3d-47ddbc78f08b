import { useState, useEffect } from 'react';
import { ResearchStep } from '@/lib/types';

interface UseResearchProgressProps {
  messages: any[];
  status: string;
  deepResearch: boolean;
}

export function useResearchProgress({ messages, status, deepResearch }: UseResearchProgressProps) {
  const [currentStep, setCurrentStep] = useState<ResearchStep | null>(null);
  const [completedSteps, setCompletedSteps] = useState<ResearchStep[]>([]);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    if (!deepResearch) {
      setCurrentStep(null);
      setCompletedSteps([]);
      setIsComplete(false);
      return;
    }

    // Get the latest assistant message
    const latestMessage = messages.filter(m => m.role === 'assistant').pop();
    if (!latestMessage) return;

    const content = latestMessage.content || '';
    
    // Parse research steps from content
    const researchPatterns = [
      { 
        pattern: /🚀\s*Starting MCP Server Research/i, 
        tool: "web_search", 
        title: "Starting research", 
        description: "Initiating comprehensive search" 
      },
      { 
        pattern: /🔍\s*Web Search/i, 
        tool: "web_search", 
        title: "Web search", 
        description: "Searching for relevant information" 
      },
      { 
        pattern: /📄\s*Analyzing Webpage/i, 
        tool: "visit_page", 
        title: "Analyzing webpage", 
        description: "Extracting information from sources" 
      },
      { 
        pattern: /📖\s*Navigating Content/i, 
        tool: "page_navigation", 
        title: "Navigating content", 
        description: "Exploring page content" 
      },
      { 
        pattern: /🔎\s*Searching Within Page/i, 
        tool: "find_on_page", 
        title: "Searching within page", 
        description: "Finding specific information" 
      },
      { 
        pattern: /✅\s*Research Complete/i, 
        tool: "reasoning_tool", 
        title: "Research complete", 
        description: "Finalizing analysis",
        complete: true 
      },
    ];

    const foundSteps: ResearchStep[] = [];
    let currentActiveStep: ResearchStep | null = null;
    let researchComplete = false;

    // Find all research indicators in the content
    for (const { pattern, tool, title, description, complete } of researchPatterns) {
      if (pattern.test(content)) {
        const step: ResearchStep = {
          id: `step-${tool}-${Date.now()}`,
          tool,
          title,
          description,
          status: complete ? "completed" : "completed",
          timestamp: new Date()
        };

        if (complete) {
          researchComplete = true;
          foundSteps.push(step);
        } else {
          foundSteps.push(step);
        }
      }
    }

    // If chat is still in progress and we have steps, the last one is active
    if (status === "submitted" && foundSteps.length > 0 && !researchComplete) {
      const lastStep = foundSteps.pop();
      if (lastStep) {
        currentActiveStep = { ...lastStep, status: "active" };
      }
    }

    // Update state
    setCompletedSteps(foundSteps);
    setCurrentStep(currentActiveStep);
    setIsComplete(researchComplete);

    // Auto-hide after completion
    if (researchComplete && status === "ready") {
      setTimeout(() => {
        setCurrentStep(null);
        setCompletedSteps([]);
        setIsComplete(false);
      }, 3000);
    }

  }, [messages, status, deepResearch]);

  // Reset when starting new research
  useEffect(() => {
    if (status === "submitted" && deepResearch) {
      setCurrentStep(null);
      setCompletedSteps([]);
      setIsComplete(false);
    }
  }, [status, deepResearch]);

  return {
    currentStep,
    completedSteps,
    isComplete
  };
}

// Helper function to filter research progress from message content
export function filterResearchProgress(content: string): string {
  // Split content into lines for easier processing
  const lines = content.split('\n');
  const filteredLines: string[] = [];
  let skipUntilNextSection = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    // Check if this line starts a research section
    const isResearchIndicator =
      /^🚀/.test(trimmedLine) ||
      /^🔍/.test(trimmedLine) ||
      /^📄/.test(trimmedLine) ||
      /^📖/.test(trimmedLine) ||
      /^🔎/.test(trimmedLine) ||
      /^✅/.test(trimmedLine) ||
      /^Initiating comprehensive search/i.test(trimmedLine) ||
      /^Looking for relevant/i.test(trimmedLine) ||
      /^Extracting.*information/i.test(trimmedLine) ||
      /^Analyzing findings/i.test(trimmedLine) ||
      /^Searching for:/i.test(trimmedLine) ||
      /^Source:/i.test(trimmedLine) ||
      /^Viewport position:/i.test(trimmedLine) ||
      /^A Google search for/i.test(trimmedLine);

    // Check if this line starts a real content section (markdown headers, etc.)
    const isContentSection =
      /^#/.test(trimmedLine) ||
      /^Based on/i.test(trimmedLine) ||
      /^Here are/i.test(trimmedLine) ||
      /^The following/i.test(trimmedLine) ||
      /^\d+\./.test(trimmedLine) ||
      /^-/.test(trimmedLine) ||
      /^\*/.test(trimmedLine);

    if (isResearchIndicator) {
      skipUntilNextSection = true;
      continue;
    }

    if (isContentSection) {
      skipUntilNextSection = false;
    }

    // Skip lines that are part of research progress
    if (skipUntilNextSection) {
      continue;
    }

    // Skip empty lines at the beginning
    if (filteredLines.length === 0 && trimmedLine === '') {
      continue;
    }

    filteredLines.push(line);
  }

  // Join lines and clean up extra whitespace
  let result = filteredLines.join('\n');

  // Remove multiple consecutive empty lines
  result = result.replace(/\n{3,}/g, '\n\n');

  // Trim the result
  result = result.trim();

  return result;
}
