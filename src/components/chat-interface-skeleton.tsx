"use client";

import React, { useEffect, useState } from "react";
import { AnimatePresence, motion } from "motion/react";
import {
  MessageCircle,
  Bot,
  X,
  ChevronUp,
  ChevronDown,
  RefreshCcw,
  LoaderCircle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/utils/utils";
import { useChatToggle } from "@/lib/providers/chat-toggle-provider";

interface Props {
  showMessages?: boolean;
}

export default function ChatInterfaceSkeleton({ showMessages = false }: Props) {
  const { isClosed, setIsClosed } = useChatToggle();
  const [isExpanded, setIsExpanded] = useState(true);
  const [chatSize] = useState({ width: 450, height: 500 });

  const toggleExpand = () => {
    if (isExpanded) {
      setIsExpanded(false);
    } else {
      setIsExpanded(true);
    }
  };

  const toggleMinimize = () => {
    setIsClosed(!isClosed);
  };

  // Auto-expand when opening from closed state
  useEffect(() => {
    if (!isClosed) {
      setIsExpanded(true);
    }
  }, [isClosed]);

  return (
    <div id="chat-interface-skeleton" className={cn("fixed z-50 bottom-4 right-4")}>
      <AnimatePresence mode="wait">
        {/* Minimized floating chat bubble */}
        {isClosed && (
          <motion.div
            key="bubble"
            initial={{ scale: 0, opacity: 0, transition: { duration: 0.1 } }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0, transition: { duration: 0.1 } }}
            className="relative group"
          >
            {/* Pulsing ring animation */}
            <div className="absolute inset-0 w-20 h-20 bg-gradient-to-r from-teal via-amber to-crimson rounded-full animate-pulse opacity-30"></div>

            {/* Main chat button */}
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className="relative w-16 h-16 bg-gradient-to-r from-teal via-amber to-crimson rounded-full flex items-center justify-center cursor-pointer shadow-[0px_0px_1px_0px_var(--primary)] hover:shadow-[0px_0px_20px_0px_var(--primary)] transition-all duration-300"
              onClick={toggleMinimize}
            >
              <MessageCircle className="h-8 w-8 text-primary" />
            </motion.div>

            {/* Tooltip */}
            <div className="absolute bottom-full right-0 mb-2 px-3 py-1 bg-background/95 backdrop-blur-sm border border-primary/50 rounded-lg text-sm text-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
              Loading Chat...
            </div>
          </motion.div>
        )}

        {/* Main chat interface */}
        {!isClosed && (
          <motion.div
            key="chat"
            initial={{ scale: [0.2, 0.4, 0.8], opacity: 0, transition: { duration: 0.1 } }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: [0.8, 0.4, 0.2], opacity: 0, transition: { duration: 0.1 } }}
            className={cn(
              "shadow-xl overflow-hidden",
              "transition-shadow transition-border duration-500 ease-in-out",
              "border border-primary/50 bg-background/100 dark:bg-background/70 backdrop-blur-sm relative",
              isExpanded
                ? "rounded-2xl flex flex-col"
                : "w-[450px] h-[150px] rounded-2xl flex flex-col cursor-pointer hover:shadow-2xl hover:border-primary/70",
            )}
            style={isExpanded ? { width: chatSize.width, height: chatSize.height } : undefined}
            onClick={!isExpanded ? toggleExpand : undefined}
          >
            {/* Header */}
            <div
              className={cn(
                "flex justify-between items-center",
                isExpanded ? "p-4 border-b" : "p-4",
              )}
            >
              <div className="flex items-center gap-3 flex-1">
                <div
                  className={cn(
                    "rounded-full flex items-center justify-center",
                    isExpanded
                      ? "w-10 h-10"
                      : "w-12 h-12 bg-gradient-to-r from-teal via-amber to-crimson shadow-lg",
                  )}
                >
                  <Bot
                    className={cn(
                      "text-foreground animate-pulse",
                      isExpanded ? "h-5 w-5" : "h-6 w-6",
                    )}
                  />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      {/* Title skeleton */}
                      <div
                        className={cn(
                          "bg-muted animate-pulse rounded",
                          isExpanded ? "h-5 w-32" : "h-5 w-28",
                        )}
                      ></div>

                      {/* Status skeleton */}
                      {isExpanded ? (
                        <div className="flex items-center gap-2 mt-2">
                          <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                          <div className="h-3 w-40 bg-muted animate-pulse rounded"></div>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2 mt-2">
                          <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                          <div className="h-3 w-20 bg-muted animate-pulse rounded"></div>
                        </div>
                      )}
                    </div>
                    {!isExpanded && (
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleMinimize();
                          }}
                          className="text-primary/50 hover:text-primary hover:bg-transparent dark:hover:bg-transparent"
                          title="Close"
                        >
                          <X strokeWidth={3} className="h-4 w-4" />
                        </Button>
                        {/* Up oscillating chevron when collapsed */}
                        <motion.div
                          animate={{ y: [0, -4, 0] }}
                          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                          className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 hover:bg-primary/20 transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleExpand();
                          }}
                        >
                          <Button
                            variant="ghost"
                            className="rounded-full hover:bg-transparent dark:hover:bg-transparent"
                          >
                            <ChevronUp className="h-4 w-4 text-primary" />
                          </Button>
                        </motion.div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              {isExpanded && (
                // Control buttons
                <div className="flex flex-row">
                  {/* Clearing the chat */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-primary/50 hover:text-primary hover:bg-transparent dark:hover:bg-transparent"
                    title="Clear Chat"
                    disabled
                  >
                    <RefreshCcw className="animate-pulse" />
                  </Button>

                  {/* Collapsing the floating chat */}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleExpand}
                    className="text-primary/50 hover:text-primary hover:bg-transparent dark:hover:bg-transparent"
                    title="Minimize"
                  >
                    <ChevronDown className="animate-pulse" />
                  </Button>

                  {/* Close to chat-bubble */}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleMinimize}
                    className="text-primary/50 hover:text-primary hover:bg-transparent dark:hover:bg-transparent"
                    title="Close"
                  >
                    <X strokeWidth={3} className="h-4 w-4 animate-pulse" />
                  </Button>
                </div>
              )}
            </div>

            {/* Message Panel */}
            <AnimatePresence>
              {isExpanded && (
                <div className="p-4 flex-grow overflow-y-auto">
                  {showMessages ? (
                    // Message panel with skeleton messages
                    <div className="space-y-4">
                      {/* Assistant message skeleton */}
                      <div className="flex justify-start">
                        <div className="flex items-start max-w-[85%] gap-2">
                          <div className="w-8 h-8 rounded-full bg-muted animate-pulse flex-shrink-0 mt-1"></div>
                          <div className="rounded-xl px-3 py-2 shadow-sm bg-background border border-foreground/20">
                            <div className="space-y-2">
                              <div className="h-4 w-48 bg-muted animate-pulse rounded"></div>
                              <div className="h-4 w-32 bg-muted animate-pulse rounded"></div>
                            </div>
                            <div className="h-3 w-12 bg-muted animate-pulse rounded mt-2 ml-auto"></div>
                          </div>
                        </div>
                      </div>

                      {/* User message skeleton */}
                      <div className="flex justify-end">
                        <div className="flex items-start max-w-[85%] gap-2">
                          <div className="rounded-xl px-3 py-2 shadow-sm bg-primary/20">
                            <div className="h-4 w-36 bg-muted animate-pulse rounded"></div>
                            <div className="h-3 w-12 bg-muted animate-pulse rounded mt-2 ml-auto"></div>
                          </div>
                          <div className="w-8 h-8 rounded-full bg-primary/20 animate-pulse flex-shrink-0 mt-1"></div>
                        </div>
                      </div>

                      {/* Another assistant message skeleton */}
                      <div className="flex justify-start">
                        <div className="flex items-start max-w-[85%] gap-2">
                          <div className="w-8 h-8 rounded-full bg-muted animate-pulse flex-shrink-0 mt-1"></div>
                          <div className="rounded-xl px-3 py-2 shadow-sm bg-background border border-foreground/20">
                            <div className="space-y-2">
                              <div className="h-4 w-56 bg-muted animate-pulse rounded"></div>
                              <div className="h-4 w-40 bg-muted animate-pulse rounded"></div>
                              <div className="h-4 w-28 bg-muted animate-pulse rounded"></div>
                            </div>
                            <div className="h-3 w-12 bg-muted animate-pulse rounded mt-2 ml-auto"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    // Empty state skeleton
                    <div className="h-full flex flex-col items-center justify-center text-center p-2">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-teal via-amber to-crimson flex items-center justify-center mb-4">
                        <LoaderCircle className="h-6 w-6 text-foreground animate-spin" />
                      </div>
                      <div className="h-6 w-32 bg-muted animate-pulse rounded mb-2"></div>
                      <div className="h-4 w-48 bg-muted animate-pulse rounded mb-4"></div>
                      <div className="grid grid-cols-1 gap-2 w-full">
                        <div className="h-10 w-full bg-muted animate-pulse rounded-md"></div>
                        <div className="h-10 w-full bg-muted animate-pulse rounded-md"></div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </AnimatePresence>

            {/* Input area skeleton */}
            <div className="p-3 border-t flex gap-2">
              <div className="flex-1 h-9 bg-muted animate-pulse rounded-md"></div>
              <div className="h-10 w-10 bg-muted animate-pulse rounded-md"></div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
