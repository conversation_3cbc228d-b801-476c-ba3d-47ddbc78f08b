"use client";

import type React from "react";
import { useState, useRef, useEffect } from "react";
import { useUser } from "@/lib/providers/user-provider";
import { useChat } from "@ai-sdk/react";
import { generateId } from "ai";
import { Cha<PERSON> } from "@/lib/types";
import { useChatToggle } from "@/lib/providers/chat-toggle-provider";
import { Button } from "@/components/ui/button";
import TextareaAutosize from "react-textarea-autosize";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import {
  Send,
  Bot,
  User,
  Sparkles,
  X,
  MessageCircle,
  Maximize2,
  ChevronDown,
  Expand,
  ChevronUp,
  RefreshCcw,
  Microscope,
} from "lucide-react";
import { cn, getName } from "@/utils/utils";
import { motion, AnimatePresence } from "motion/react";
import Link from "next/link";
import { useChatSession } from "@/lib/hooks/use-chat-sessions";

interface Prop {
  storageKey: string;
  chat: Chat | null;
}

export function UserChatInterface({ chat, storageKey }: Prop) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { isClosed, setIsClosed } = useChatToggle();
  const [chatSize, setChatSize] = useState({ width: 450, height: 500 });
  const [isResizing, setIsResizing] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const chatRef = useRef<HTMLDivElement>(null);
  const sizeRef = useRef({ width: chatSize.width, height: chatSize.height });
  const rafIdRef = useRef<number | null>(null);
  const [name, setName] = useState("");
  const { user } = useUser();
  const [deepResearch, setDeepResearch] = useState(false);
  const [chatId, setChatId] = useState(() => chat?.chat_id || generateId());
  const { isSessionActive, startChatTracking, clearSession, isTrackingActivity, activeChatId } =
    useChatSession({ key: storageKey });
  const { status, messages, input, setInput, setMessages, handleInputChange, handleSubmit } =
    useChat({
      id: chatId,
      api: "/api/users-chat", // /api/deep-reasoning
      initialMessages: chat?.messages || [],
      experimental_prepareRequestBody({ messages, id }) {
        return { message: messages[messages.length - 1], id, deepResearch };
      },
      onFinish: () => {
        setTimeout(() => inputRef.current?.focus(), 100);
      },
    });

  useEffect(() => {
    if (user) {
      const moniker = getName(user.user_metadata.name);
      setName(moniker);
    }
  }, [user, setName]);

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: "smooth",
        block: "end",
        inline: "nearest",
      });
    }
  };

  useEffect(() => {
    if (isExpanded && !isClosed) {
      const scrollTimer = requestAnimationFrame(() => {
        scrollToBottom();
      });

      return () => {
        cancelAnimationFrame(scrollTimer);
      };
    }
  }, [messages, isExpanded, isClosed, status]);

  useEffect(() => {
    if (!isClosed && isExpanded) {
      const timer = setTimeout(() => {
        scrollToBottom();
        inputRef.current?.focus();
      }, 150);

      return () => {
        clearTimeout(timer);
      };
    }
  }, [isClosed, isExpanded]);

  const toggleExpand = () => {
    if (isExpanded) {
      setIsExpanded(false);
    } else {
      setIsExpanded(true);

      const timer = window.setTimeout(() => {
        scrollToBottom();
        inputRef.current?.focus();
      }, 200);

      return () => {
        window.clearTimeout(timer);
      };
    }
  };

  const toggleMinimize = () => {
    setIsClosed(!isClosed);
  };

  const handleResizeMouseDown = (e: React.MouseEvent) => {
    if (!isExpanded) return;
    e.stopPropagation();
    e.preventDefault();

    setIsResizing(true);
    document.body.style.userSelect = "none";

    const startX = e.clientX;
    const startY = e.clientY;
    const startWidth = sizeRef.current.width;
    const startHeight = sizeRef.current.height;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;
      const newW = Math.max(450, Math.min(800, startWidth - deltaX));
      const newH = Math.max(500, Math.min(760, startHeight - deltaY));

      // update the ref
      sizeRef.current = { width: newW, height: newH };

      if (rafIdRef.current === null) {
        rafIdRef.current = window.requestAnimationFrame(() => {
          if (chatRef.current) {
            chatRef.current.style.width = `${sizeRef.current.width}px`;
            chatRef.current.style.height = `${sizeRef.current.height}px`;
          }
          rafIdRef.current = null;
        });
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.body.style.userSelect = "";
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);

      // Commit final size into React state
      setChatSize({ ...sizeRef.current });

      if (rafIdRef.current !== null) {
        window.cancelAnimationFrame(rafIdRef.current);
        rafIdRef.current = null;
      }
    };

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  };

  const handleChatSubmit = (
    e: React.FormEvent<HTMLFormElement> | React.KeyboardEvent<HTMLTextAreaElement>,
  ) => {
    e.preventDefault();

    if (!input.trim()) return;

    handleSubmit(e);

    // Start tracking if not already active (for new chats)
    if (!isSessionActive) {
      startChatTracking(chatId);
    }

    setTimeout(() => inputRef.current?.focus(), 50);
  };

  const clearChat = () => {
    setMessages([]);
    clearSession();
    setChatId(generateId());
  };

  useEffect(() => {
    if (!isClosed) {
      setIsExpanded(true);
      if (isSessionActive && activeChatId) {
        startChatTracking(activeChatId);
      }
      const timer = window.setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
      return () => {
        window.clearTimeout(timer);
      };
    }
  }, [activeChatId, isClosed, isSessionActive, startChatTracking]);

  useEffect(() => {
    // Start tracking when component mounts with current chatId
    if (chatId && messages.length > 0 && !isSessionActive) {
      startChatTracking(chatId);
    }
  }, [chatId, messages, isSessionActive, startChatTracking]);

  return (
    <>
      <div id="chat-interface" className={cn("fixed z-50 bottom-4 right-4")}>
        <AnimatePresence mode="wait">
          {/* Minimized floating chat bubble */}
          {isClosed && (
            <motion.div
              key="bubble"
              initial={{ scale: 0, opacity: 0, transition: { duration: 0.1 } }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0, transition: { duration: 0.1 } }}
              className="relative group"
            >
              {/* Main chat button */}
              <Tooltip>
                <TooltipTrigger>
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className="relative w-16 h-16 bg-gradient-to-r from-teal via-amber to-crimson rounded-full flex items-center justify-center cursor-pointer shadow-[0px_0px_1px_0px_var(--primary)] hover:shadow-[0px_0px_20px_0px_var(--primary)] transition-all duration-300"
                    onClick={toggleMinimize}
                  >
                    <MessageCircle className="h-8 w-8 text-primary" />
                  </motion.div>
                </TooltipTrigger>
                <TooltipContent
                  className="bg-background/95 backdrop-blur-sm border border-primary/50 rounded-lg text-sm text-foreground relative z-100"
                  arrowClassName="fill-background bg-background rounded-[2px] z-10"
                >
                  <p>Chat with AI Assistant</p>
                </TooltipContent>
              </Tooltip>
            </motion.div>
          )}

          {/* Main chat interface */}
          {!isClosed && (
            <motion.div
              ref={chatRef}
              key="chat"
              initial={{ scale: [0.2, 0.4, 0.8], opacity: 0, transition: { duration: 0.1 } }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: [0.8, 0.4, 0.2], opacity: 0, transition: { duration: 0.1 } }}
              className={cn(
                "shadow-xl overflow-hidden",
                isResizing ? "" : "transition-shadow transition-border duration-500 ease-in-out",
                "border border-primary/50 bg-background/100 dark:bg-background/70 backdrop-blur-sm relative",
                isExpanded
                  ? "rounded-2xl flex flex-col"
                  : "w-[450px] h-37 rounded-2xl flex flex-col cursor-pointer hover:shadow-2xl hover:border-primary/70",
              )}
              style={isExpanded ? { width: chatSize.width, height: chatSize.height } : undefined}
              onClick={!isExpanded ? toggleExpand : undefined}
            >
              {/* Resize handle */}
              {isExpanded && (
                <div
                  className="absolute top-0 left-0 w-4 h-4 cursor-nw-resize opacity-50 hover:opacity-100 transition-opacity z-10"
                  onMouseDown={handleResizeMouseDown}
                >
                  <Maximize2 className="h-3 w-3 text-primary" />
                </div>
              )}
              {/* Header */}
              <div
                className={cn(
                  "flex justify-between items-center",
                  isExpanded ? "p-4 border-b" : "p-4",
                )}
              >
                <div className="flex items-center gap-3 flex-1">
                  <div
                    className={cn(
                      "rounded-full flex items-center justify-center",
                      isExpanded
                        ? "w-10 h-10"
                        : "w-12 h-12 bg-gradient-to-r from-teal via-amber to-crimson shadow-lg",
                    )}
                  >
                    <Bot
                      className={cn(
                        "text-foreground",
                        isExpanded ? "h-5 w-5" : "h-6 w-6 text-white",
                      )}
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <h2
                          className={cn(
                            "font-bold text-primary",
                            isExpanded ? "text-lg" : "text-lg",
                          )}
                        >
                          MCP Assistant
                        </h2>
                        {isExpanded ? (
                          <div className="flex items-center gap-2 mt-1">
                            {!chat?.messages && (
                              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            )}
                            <span className="text-xs text-foreground/80 font-medium">
                              {`Hello, ${name}`}
                            </span>
                          </div>
                        ) : (
                          // Online indicator when collapsed
                          <div className="flex items-center gap-2 mt-1">
                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span className="text-xs text-foreground/80 font-medium">
                              {`Hello, ${name}`}
                            </span>
                          </div>
                        )}
                      </div>
                      {!isExpanded && (
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={toggleMinimize}
                            className="text-primary/50 hover:text-primary hover:bg-transparent dark:hover:bg-transparent"
                            title="Close"
                          >
                            <X strokeWidth={3} className="h-4 w-4" />
                          </Button>
                          {/* Up oscillating chevron when collapsed */}
                          <motion.div
                            animate={{ y: [0, -4, 0] }}
                            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                            className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 hover:bg-primary/20 transition-colors"
                            onClick={toggleExpand}
                          >
                            <Button
                              variant="ghost"
                              className="rounded-full hover:bg-transparent dark:hover:bg-transparent"
                            >
                              <ChevronUp className="h-4 w-4 text-primary" />
                            </Button>
                          </motion.div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {isExpanded && (
                  // Control buttons
                  <div className="flex flex-row">
                    {/* Clearing the chat */}
                    <Tooltip>
                      <TooltipTrigger>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={clearChat}
                          className="text-primary/50 hover:text-primary hover:bg-transparent dark:hover:bg-transparent"
                        >
                          <RefreshCcw />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Clear Chat</p>
                      </TooltipContent>
                    </Tooltip>
                    {/* Collapsing the floating chat */}
                    <Tooltip>
                      <TooltipTrigger>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={toggleExpand}
                          className="text-primary/50 hover:text-primary hover:bg-transparent dark:hover:bg-transparent"
                        >
                          <ChevronDown />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Minimize</p>
                      </TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-primary/50 hover:text-primary hover:bg-transparent dark:hover:bg-transparent"
                        >
                          <Link href="/chat">
                            <Expand />
                          </Link>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Continue In Web-App</p>
                      </TooltipContent>
                    </Tooltip>
                    {/* Close to chat-bubble */}
                    <Tooltip>
                      <TooltipTrigger>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={toggleMinimize}
                          className="text-primary/50 hover:text-primary hover:bg-transparent dark:hover:bg-transparent"
                        >
                          <X strokeWidth={3} className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Close</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                )}
              </div>

              {/* Message Pannel */}
              <AnimatePresence>
                {isExpanded && (
                  <div className="p-4 flex-grow overflow-y-auto" data-chat-window>
                    {messages.length > 0 ? (
                      // Message pannel with messages
                      <div className="space-y-4">
                        {messages.map((message) => (
                          <motion.div
                            key={message.id}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3 }}
                            className={cn(
                              "flex",
                              message.role === "user" ? "justify-end" : "justify-start",
                            )}
                          >
                            <div className="flex items-start max-w-[85%] gap-2">
                              {message.role === "assistant" && (
                                <div className="w-8 h-8 rounded-full flex-shrink-0 flex items-center justify-center mt-1">
                                  <Bot className="h-4 w-4 text-foreground" />
                                </div>
                              )}
                              <div
                                className={cn(
                                  "rounded-xl px-3 py-2 shadow-sm",
                                  message.role === "user"
                                    ? "bg-primary text-background"
                                    : "bg-background border border-foreground/20",
                                )}
                              >
                                <p className="leading-relaxed text-sm">{message.content}</p>
                                <p className="text-xs opacity-70 mt-1 text-right">
                                  {message.createdAt?.toLocaleTimeString([], {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                  })}
                                </p>
                              </div>
                              {message.role === "user" && (
                                <div className="w-8 h-8 rounded-full bg-primary flex-shrink-0 flex items-center justify-center mt-1">
                                  <User className="h-4 w-4 text-background" />
                                </div>
                              )}
                            </div>
                          </motion.div>
                        ))}
                        {status === "submitted" && (
                          <div className="mr-2 flex flex-row gap-4 items-center h-fit">
                            <div className="w-8 h-8 rounded-full flex-shrink-0 flex items-center justify-center mt-1">
                              <Bot className="h-4 w-4 text-foreground" />
                            </div>
                            <div className="flex items-center gap-1">
                              <motion.div
                                className="w-1 h-1 bg-primary/80 rounded-full"
                                animate={{ opacity: [0.6, 1, 0.6] }}
                                transition={{ duration: 1.5, repeat: Infinity, delay: 0 }}
                              ></motion.div>

                              <motion.div
                                className="w-1 h-1 bg-primary/60 rounded-full"
                                animate={{ opacity: [0.4, 1, 0.4] }}
                                transition={{ duration: 1.5, repeat: Infinity, delay: 0.5 }}
                              ></motion.div>

                              <motion.div
                                className="w-1 h-1 bg-primary/40 rounded-full"
                                animate={{ opacity: [0.2, 1, 0.2] }}
                                transition={{ duration: 1.5, repeat: Infinity, delay: 1 }}
                              ></motion.div>
                            </div>
                          </div>
                        )}
                        <div ref={messagesEndRef} />
                      </div>
                    ) : (
                      // Message pannel without messages
                      <div className="h-full flex flex-col items-center justify-center text-center p-2">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-r from-teal via-amber to-crimson flex items-center justify-center mb-4">
                          <Sparkles className="h-6 w-6 text-foreground" />
                        </div>
                        <h3 className="text-lg font-bold mb-2 gradient-text">How can I help?</h3>
                        <p className="text-foreground text-sm mb-4">
                          Ask me anything about MCP servers
                        </p>
                        <div className="grid grid-cols-1 gap-2 w-full">
                          {["Best MCP servers for IoT?", "How to integrate QuantumMCP?"].map(
                            (suggestion, i) => (
                              <Button
                                key={i}
                                variant="outline"
                                className="text-xs dark:bg-transparent h-auto py-2 px-3 hover:border-none hover:shadow-primary hover:shadow-xs"
                                onClick={() => {
                                  setInput(suggestion);
                                  inputRef.current?.focus();
                                }}
                              >
                                {suggestion}
                              </Button>
                            ),
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </AnimatePresence>

              {/* Text area */}
              <form onSubmit={handleChatSubmit} className="p-3 border-t flex gap-2">
                <div data-chat-input className="flex flex-row w-full items-center gap-2">
                  <Tooltip>
                    <TooltipTrigger>
                      <Button
                        size={"icon"}
                        type="button"
                        variant={deepResearch ? "default" : "ghost"}
                        onClick={() => setDeepResearch(!deepResearch)}
                        disabled={status !== "ready"}
                        className={cn(
                          !deepResearch
                            ? "hover:text-primary hover:border hover:border-primary hover:dark:border-primary border border-foreground/20"
                            : "border border-foreground/20",
                        )}
                      >
                        <Microscope />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Deep Research</p>
                    </TooltipContent>
                  </Tooltip>
                  <TextareaAutosize
                    ref={inputRef}
                    placeholder="Ask me anything about MCP servers..."
                    disabled={status !== "ready"}
                    className={cn(
                      "placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border border-primary/40 bg-transparent px-3 py-2 text-sm transition-[color,box-shadow] outline-none disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 resize-none focus-within:outline-none",
                      "focus-visible:ring-[1.25px] focus-visible:ring-primary/40",
                      "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
                    )}
                    minRows={1}
                    maxRows={3}
                    spellCheck={false}
                    autoComplete="off"
                    autoCorrect="off"
                    name="message"
                    rows={1}
                    required
                    value={input}
                    onChange={handleInputChange}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        if (input.trim()) {
                          handleChatSubmit(e as React.KeyboardEvent<HTMLTextAreaElement>);
                        }
                      }
                    }}
                  />
                </div>
                <Button
                  type="submit"
                  size="icon"
                  disabled={!input || status !== "ready"}
                  className={`bg-transparent border border-primary/40 transition-opacity h-10 w-10 ${input && "ring-[1.25px] ring-primary/40"}`}
                >
                  <Send className="h-4 w-4 text-foreground" />
                </Button>
              </form>
            </motion.div>
          )}
        </AnimatePresence>
        {process.env.NODE_ENV === "development" && (
          <div className="text-xs text-gray-500 p-2">
            <div>Active Chat ID: {activeChatId}</div>
            <div>Session Active: {isSessionActive ? "Yes" : "No"}</div>
            <div>Tracking Activity: {isTrackingActivity ? "Yes" : "No"}</div>
          </div>
        )}
      </div>
    </>
  );
}
