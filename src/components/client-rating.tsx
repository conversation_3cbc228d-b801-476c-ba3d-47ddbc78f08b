"use client";

import { useEffect, useState } from "react";
import Rating from "@mui/material/Rating";
import StarBorderIcon from "@mui/icons-material/StarBorder";

interface ClientRatingProps {
  name: string;
  value: number;
  precision: number;
}

export default function ClientRating({ name, value, precision }: ClientRatingProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null; // Or a loading spinner, or a placeholder
  }

  return (
    <Rating
      name={name}
      value={value}
      precision={precision}
      emptyIcon={<StarBorderIcon fontSize="inherit" className="text-foreground/50" />}
      readOnly
    />
  );
}
