import { Button } from "@/components/ui/button";
import { ratingLabels } from "@/utils/utils";
import { Badge } from "@/components/ui/badge";
import { Globe, Calendar, Users } from "lucide-react";
import { RiGithubLine } from "react-icons/ri";
import type { ServerType } from "@/lib/types";
import Image from "next/image";
import Link from "next/link";
import ClientRating from "./client-rating";

interface ServerDetailsProps {
  server: ServerType;
}

export default function McpServerDetails({ server }: ServerDetailsProps) {
  return (
    <div className="bg-secondary dark:bg-card rounded-2xl p-8 shadow-lg backdrop-blur-sm border border-input">
      <div className="flex flex-col md:flex-row gap-8">
        <div className="flex-shrink-0">
          <div className="h-32 w-32 rounded-xl flex items-center justify-center overflow-hidden">
            {server.logo ? (
              <Image
                src={server.logo || "/placeholder.svg"}
                alt={`${server.name} logo`}
                width={200}
                height={200}
                className="h-24 w-24 object-contain"
              />
            ) : (
              <div className="h-32 w-32 bg-primary flex items-center justify-center">
                <span className="text-5xl font-bold text-white">{server.name.charAt(0)}</span>
              </div>
            )}
          </div>
        </div>

        <div className="flex-1">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold">{server.name}</h1>
              <div className="mt-2 flex flex-row gap-2 items-center text-foreground/50">
                <ClientRating
                  name={`${server.name}-rating`}
                  value={server.rating}
                  precision={0.5}
                />
                <div>
                  {server.rating} - {ratingLabels[server.rating]} ({server.reviewCount} reviews)
                </div>
              </div>
            </div>

            <div className="flex flex-wrap gap-3">
              {server.githubUrl && (
                <Button
                  asChild
                  variant="outline"
                  className="gap-2 bg-transparent hover:text-primary hover:border-primary"
                >
                  <Link href={server.githubUrl} target="_blank" rel="noopener noreferrer">
                    <RiGithubLine className="h-4 w-4" />
                    GitHub
                  </Link>
                </Button>
              )}
              {server.websiteUrl && (
                <Button
                  asChild
                  variant="outline"
                  className="gap-2 bg-transparent hover:text-primary hover:border-primary"
                >
                  <Link href={server.websiteUrl} target="_blank" rel="noopener noreferrer">
                    <Globe className="h-4 w-4" />
                    Website
                  </Link>
                </Button>
              )}
            </div>
          </div>

          <p className="mt-4">{server.description}</p>

          <div className="mt-6 flex flex-wrap gap-2">
            {server.tags.map((tag, index) => (
              <Badge key={index} variant="secondary" className="bg-zinc-700/50">
                {tag}
              </Badge>
            ))}
          </div>

          <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>Updated: {server.lastUpdated}</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span>{server.userCount.toLocaleString()} active users</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{server.license} License</Badge>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
