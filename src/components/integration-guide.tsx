"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Copy, Check } from "lucide-react";
import { useState } from "react";
import type { ServerType } from "@/lib/types";

interface IntegrationGuideProps {
  server: ServerType;
}

export function IntegrationGuide({ server }: IntegrationGuideProps) {
  const [copied, setCopied] = useState<string | null>(null);

  const languagageButtonClass =
    "cursor-pointer bg-foreground text-background data-[state=active]:text-background data-[state=active]:bg-primary hover:data-[state=inactive]:text-primary dark:data-[state=active]:text-white dark:data-[state=active]:bg-primary transition-colors";

  const handleCopy = (text: string, id: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        setCopied(id);
        setTimeout(() => setCopied(null), 2000);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  const CodeBlock = ({ code, language, id }: { code: string; language: string; id: string }) => (
    <div className="relative">
      <pre className="bg-foreground/50 dark:bg-foreground/10 text-white dark:selection:text-white p-4 rounded-lg overflow-x-auto">
        <code className={`language-${language}`}>{code}</code>
      </pre>
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-2 right-2 text-white"
        onClick={() => handleCopy(code, id)}
      >
        {copied === id ? (
          <Check className="h-4 w-4 text-green-500" />
        ) : (
          <Copy className="h-4 w-4" />
        )}
      </Button>
    </div>
  );

  return (
    <div className="bg-secondary dark:bg-card rounded-2xl p-8">
      <h2 className="text-4xl text-primary text-center font-bold mb-6">Integration Guide</h2>

      <Tabs defaultValue="javascript">
        <TabsList className="mb-6 gap-2">
          <TabsTrigger value="javascript" className={languagageButtonClass}>
            JavaScript
          </TabsTrigger>
          <TabsTrigger value="python" className={languagageButtonClass}>
            Python
          </TabsTrigger>
          <TabsTrigger value="java" className={languagageButtonClass}>
            Java
          </TabsTrigger>
          <TabsTrigger value="csharp" className={languagageButtonClass}>
            C#
          </TabsTrigger>
        </TabsList>

        <TabsContent value="javascript">
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold mb-2">Installation</h3>
              <CodeBlock
                id="js-install"
                language="bash"
                code={`npm install ${server.name.toLowerCase()}-client`}
              />
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-2">Basic Usage</h3>
              <CodeBlock
                id="js-usage"
                language="javascript"
                code={`import { ${server.name}Client } from '${server.name.toLowerCase()}-client';

// Initialize the client
const client = new ${server.name}Client({
  apiKey: 'YOUR_API_KEY',
  endpoint: 'https://api.example.com/v1'
});

// Connect to the server
await client.connect();

// Send a command
const response = await client.sendCommand({
  action: 'GET_STATUS',
  parameters: {
    deviceId: 'device-123'
  }
});

console.log(response);

// Close the connection when done
client.disconnect();`}
              />
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-2">Error Handling</h3>
              <CodeBlock
                id="js-error"
                language="javascript"
                code={`try {
  const response = await client.sendCommand({
    action: 'INVALID_ACTION',
    parameters: {}
  });
} catch (error) {
  console.error('Error:', error.message);
  console.error('Error code:', error.code);
  
  // Handle specific error types
  if (error.code === 'CONNECTION_ERROR') {
    // Attempt to reconnect
    await client.reconnect();
  }
}`}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="python">
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold mb-2">Installation</h3>
              <CodeBlock
                id="py-install"
                language="bash"
                code={`pip install ${server.name.toLowerCase()}-client`}
              />
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-2">Basic Usage</h3>
              <CodeBlock
                id="py-usage"
                language="python"
                code={`from ${server.name.toLowerCase()}_client import ${server.name}Client

# Initialize the client
client = ${server.name}Client(
    api_key="YOUR_API_KEY",
    endpoint="https://api.example.com/v1"
)

# Connect to the server
client.connect()

# Send a command
response = client.send_command(
    action="GET_STATUS",
    parameters={
        "device_id": "device-123"
    }
)

print(response)

# Close the connection when done
client.disconnect()`}
              />
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-2">Error Handling</h3>
              <CodeBlock
                id="py-error"
                language="python"
                code={`try:
    response = client.send_command(
        action="INVALID_ACTION",
        parameters={}
    )
except Exception as e:
    print(f"Error: {str(e)}")
    print(f"Error code: {e.code}")
    
    # Handle specific error types
    if e.code == "CONNECTION_ERROR":
        # Attempt to reconnect
        client.reconnect()`}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="java">
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold mb-2">Installation</h3>
              <CodeBlock
                id="java-install"
                language="xml"
                code={`<dependency>
    <groupId>com.example</groupId>
    <artifactId>${server.name.toLowerCase()}-client</artifactId>
    <version>1.0.0</version>
</dependency>`}
              />
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-2">Basic Usage</h3>
              <CodeBlock
                id="java-usage"
                language="java"
                code={`import com.example.${server.name.toLowerCase()}.${server.name}Client;
import com.example.${server.name.toLowerCase()}.Command;
import com.example.${server.name.toLowerCase()}.Response;

public class Example {
    public static void main(String[] args) {
        // Initialize the client
        ${server.name}Client client = new ${server.name}Client.Builder()
            .apiKey("YOUR_API_KEY")
            .endpoint("https://api.example.com/v1")
            .build();
            
        try {
            // Connect to the server
            client.connect();
            
            // Create command
            Command command = new Command.Builder()
                .action("GET_STATUS")
                .parameter("deviceId", "device-123")
                .build();
                
            // Send command
            Response response = client.sendCommand(command);
            System.out.println(response.toString());
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // Close the connection when done
            client.disconnect();
        }
    }
}`}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="csharp">
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold mb-2">Installation</h3>
              <CodeBlock
                id="csharp-install"
                language="bash"
                code={`dotnet add package ${server.name.toLowerCase()}-client`}
              />
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-2">Basic Usage</h3>
              <CodeBlock
                id="csharp-usage"
                language="csharp"
                code={`using ${server.name}.Client;
using System;
using System.Threading.Tasks;

namespace Example
{
    class Program
    {
        static async Task Main(string[] args)
        {
            // Initialize the client
            var client = new ${server.name}Client(
                apiKey: "YOUR_API_KEY",
                endpoint: "https://api.example.com/v1"
            );
            
            try
            {
                // Connect to the server
                await client.ConnectAsync();
                
                // Send a command
                var response = await client.SendCommandAsync(
                    action: "GET_STATUS",
                    parameters: new Dictionary<string, object>
                    {
                        { "deviceId", "device-123" }
                    }
                );
                
                Console.WriteLine(response);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
            finally
            {
                // Close the connection when done
                await client.DisconnectAsync();
            }
        }
    }
}`}
              />
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="mt-8">
        <h3 className="text-xl font-semibold mb-4">Additional Resources</h3>
        <ul className="space-y-2">
          <li>
            <a href="#" className="text-primary hover:underline">
              Full API Documentation
            </a>
          </li>
          <li>
            <a href="#" className="text-primary hover:underline">
              Example Projects
            </a>
          </li>
          <li>
            <a href="#" className="text-primary hover:underline">
              Troubleshooting Guide
            </a>
          </li>
          <li>
            <a href="#" className="text-primary hover:underline">
              Community Forum
            </a>
          </li>
        </ul>
      </div>
    </div>
  );
}
