"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Rating from "@mui/material/Rating";
import StarBorderIcon from "@mui/icons-material/StarBorder";
import { ratingLabels } from "@/utils/utils";
import { ExternalLink } from "lucide-react";
import { RiGithubLine } from "react-icons/ri";
import type { ServerType } from "@/lib/types";
import { motion } from "motion/react";
import Image from "next/image";

interface ServerCardProps {
  server: ServerType;
  featured?: boolean;
}

export default function McpServerCard({ server }: ServerCardProps) {
  return (
    <motion.div
      whileHover={{ y: -8, scale: 1.02 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
      className="h-full group"
    >
      <Card className="h-full overflow-hidden bg-gradient-to-br from-background via-background to-background/95 border border-primary/20 shadow-xl hover:shadow-2xl hover:shadow-primary/10 transition-all duration-500 rounded-2xl backdrop-blur-sm">
        <CardHeader className="p-0 relative">
          <div className="h-52 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent relative overflow-hidden">
            {/* Animated background pattern */}
            <div className="absolute inset-0 opacity-30">
              <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.2),transparent_70%)]"></div>
              <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_70%_80%,rgba(255,119,198,0.2),transparent_70%)]"></div>
            </div>

            {/* Floating particles effect */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute top-4 left-4 w-2 h-2 bg-primary/30 rounded-full animate-float"></div>
              <div
                className="absolute top-8 right-8 w-1 h-1 bg-amber/40 rounded-full animate-float"
                style={{ animationDelay: "-1s" }}
              ></div>
              <div
                className="absolute bottom-6 left-8 w-1.5 h-1.5 bg-teal/30 rounded-full animate-float"
                style={{ animationDelay: "-2s" }}
              ></div>
            </div>

            {server.logo ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative">
                  <div className="absolute inset-0 bg-primary/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-500"></div>
                  <div className="relative h-28 w-28 rounded-2xl bg-background/90 backdrop-blur-sm p-3 flex items-center justify-center shadow-xl border border-primary/20 group-hover:scale-110 transition-transform duration-500">
                    <Image
                      src={server.logo || "/placeholder.svg"}
                      alt={`${server.name} logo`}
                      width={200}
                      height={200}
                      className="object-contain"
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-teal via-primary to-amber rounded-full blur-xl group-hover:blur-2xl transition-all duration-500 opacity-50"></div>
                  <div className="relative h-28 w-28 rounded-2xl bg-gradient-to-br from-teal via-primary to-amber flex items-center justify-center shadow-xl group-hover:scale-110 transition-transform duration-500">
                    <span className="text-4xl font-bold text-white">{server.name.charAt(0)}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Version badge */}
            <div className="absolute top-4 right-4">
              <Badge className="bg-background/90 text-foreground border border-primary/20 backdrop-blur-sm shadow-lg">
                v{server.version}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          <div className="space-y-3">
            <h3 className="text-2xl font-bold gradient-text group-hover:text-primary transition-colors duration-300">
              {server.name}
            </h3>

            <div className="flex items-center gap-3">
              <Rating
                name={`${server.name}-rating`}
                value={server.rating}
                precision={0.5}
                emptyIcon={<StarBorderIcon fontSize="inherit" className="text-foreground/30" />}
                readOnly
                size="small"
              />
              <span className="text-sm font-medium text-foreground/70">
                {server.rating} • {ratingLabels[server.rating]}
              </span>
            </div>
          </div>

          <p className="text-foreground/80 line-clamp-3 leading-relaxed">{server.description}</p>

          <div className="flex flex-wrap gap-2">
            {server.tags.slice(0, 3).map((tag, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="bg-primary/10 text-primary border border-primary/20 hover:bg-primary/20 transition-colors duration-300"
              >
                {tag}
              </Badge>
            ))}
            {server.tags.length > 3 && (
              <Badge variant="outline" className="text-foreground/60 border-foreground/20">
                +{server.tags.length - 3} more
              </Badge>
            )}
          </div>
        </CardContent>
        <CardFooter className="p-6 pt-0 flex justify-between items-center border-t border-primary/10">
          <Button
            asChild
            className="bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl hover:shadow-primary/25 transition-all duration-300 transform hover:-translate-y-0.5 font-medium"
          >
            <Link href={`/marketplace/server/${server.id}`} className="flex items-center gap-2">
              View Details
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 8l4 4m0 0l-4 4m4-4H3"
                />
              </svg>
            </Link>
          </Button>

          <div className="flex gap-2">
            {server.githubUrl && (
              <a href={server.githubUrl} target="_blank" rel="noopener noreferrer">
                <Button
                  variant="outline"
                  size="icon"
                  className="border border-primary/20 hover:border-primary hover:bg-primary/10 hover:text-primary transition-all duration-300 backdrop-blur-sm"
                >
                  <RiGithubLine className="h-4 w-4" />
                  <span className="sr-only">GitHub</span>
                </Button>
              </a>
            )}
            {server.websiteUrl && (
              <a href={server.websiteUrl} target="_blank" rel="noopener noreferrer">
                <Button
                  variant="outline"
                  size="icon"
                  className="border border-primary/20 hover:border-primary hover:bg-primary/10 hover:text-primary transition-all duration-300 backdrop-blur-sm"
                >
                  <ExternalLink className="h-4 w-4" />
                  <span className="sr-only">Website</span>
                </Button>
              </a>
            )}
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  );
}
