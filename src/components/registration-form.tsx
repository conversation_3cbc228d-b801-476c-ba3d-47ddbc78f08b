"use client";
import { useCallback, useState } from "react";

// zod dependencies
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { register } from "@/lib/supabase-server-actions";
import { RegistrationSchema } from "@/lib/schemas/auth-form-schemas";

// shadcn form components
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import { Turnstile } from "@marsidev/react-turnstile";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { Eye, EyeClosed, Loader2 } from "lucide-react";
import { Provider } from "@supabase/supabase-js";
import { oAuthSignIn } from "@/lib/supabase-client-actions";

export function RegistrationForm() {
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [captchaToken, setCaptchaToken] = useState("");

  // Zod form definition.
  const form = useForm<z.infer<typeof RegistrationSchema>>({
    resolver: zodResolver(RegistrationSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  // Form values will be type-safe and validated by Zod.
  const handleSubmit = async (values: z.infer<typeof RegistrationSchema>) => {
    if (!agreedToTerms) {
      toast.warning("Terms Agreement Required", {
        description: "Please agree to the terms and conditions to continue.",
      });
      return;
    }

    setIsLoading(true);
    await register(values, captchaToken);

    // Simulate API call
    setTimeout(() => {
      toast("Demo Mode", {
        description: "This is a demo. Account creation is not implemented.",
      });
      setIsLoading(false);
    }, 1500);
  };

  const togglePasswordVisibility = () => {
    setShowPassword((prev_state) => !prev_state);
  };

  const togglePassConfirmationVisibility = () => {
    setShowConfirmation((prev_state) => !prev_state);
  };

  const handleOauthClick = useCallback((provider: Provider, origin: string) => {
    oAuthSignIn(provider, origin);
    setIsLoading(true);

    setTimeout(() => {
      setIsLoading(false);
    }, 3000);
  }, []);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="space-y-2">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Name</FormLabel>
                <FormLabel htmlFor="name">Name</FormLabel>
                <FormControl>
                  <Input
                    type="text"
                    id="name"
                    placeholder="John Doe"
                    autoCapitalize="none"
                    autoComplete="off"
                    autoCorrect="off"
                    disabled={isLoading}
                    required
                    className="bg-input focus-visible:ring-primary focus-visible:border-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-2">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Email</FormLabel>
                <FormLabel htmlFor="email">Email</FormLabel>
                <FormControl>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    autoCapitalize="none"
                    autoComplete="on"
                    autoCorrect="off"
                    disabled={isLoading}
                    required
                    className="bg-input focus-visible:ring-primary focus-visible:border-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-2">
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Password</FormLabel>
                <FormLabel htmlFor="password">Password</FormLabel>
                <div className="flex flex-row items-center gap-2 bg-input dark:bg-input/30 rounded-md border border-input focus-within:ring-3 focus-within:ring-primary 2 focus-within:border-none">
                  <FormControl>
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="••••••••"
                      autoCapitalize="none"
                      autoComplete="off"
                      autoCorrect="off"
                      disabled={isLoading}
                      required
                      className="bg-transparent dark:bg-transparent border-none focus-visible:border-none focus-visible:ring-0"
                      {...field}
                    />
                  </FormControl>
                  <span className="cursor-pointer pr-2" onClick={togglePasswordVisibility}>
                    {showPassword ? <Eye className="text-primary" /> : <EyeClosed />}
                  </span>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-2">
          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Confirm Password</FormLabel>
                <FormLabel htmlFor="confirmPassword">Confirm Password</FormLabel>
                <div className="flex flex-row items-center gap-2 bg-input dark:bg-input/30 rounded-md border border-input focus-within:ring-3 focus-within:ring-primary 2 focus-within:border-none">
                  <FormControl>
                    <Input
                      id="confirmPassword"
                      type={showConfirmation ? "text" : "password"}
                      placeholder="••••••••"
                      autoCapitalize="none"
                      autoComplete="off"
                      autoCorrect="off"
                      disabled={isLoading}
                      required
                      className="bg-transparent dark:bg-transparent border-none focus-visible:border-none focus-visible:ring-0"
                      {...field}
                    />
                  </FormControl>
                  <span className="cursor-pointer pr-2" onClick={togglePassConfirmationVisibility}>
                    {showConfirmation ? <Eye className="text-primary" /> : <EyeClosed />}
                  </span>
                </div>
                <FormMessage className="" />
              </FormItem>
            )}
          />
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="terms"
            className="cursor-pointer data-[state=checked]:text-white border-foreground/50 hover:border-primary"
            checked={agreedToTerms}
            onCheckedChange={(checked) => setAgreedToTerms(checked as boolean)}
          />
          <div className="text-sm font-normal">
            I agree to the{" "}
            <a href="#" className="text-primary hover:underline">
              Terms of Service
            </a>{" "}
            and{" "}
            <a href="#" className="text-primary hover:underline">
              Privacy Policy
            </a>
          </div>
        </div>

        <Turnstile
          className="mx-auto"
          siteKey={process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY!}
          onSuccess={(token) => setCaptchaToken(token)}
        />

        <Button
          type="submit"
          className="w-full text-white bg-primary/80 hover:bg-primary transition-colors"
          disabled={isLoading || !agreedToTerms}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating account...
            </>
          ) : (
            "Create Account"
          )}
        </Button>

        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-foreground/50"></div>
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-foreground text-background px-2">Or sign up with</span>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Button
            variant="outline"
            type="button"
            className="w-full hover:border hover:border-primary dark:hover:border dark:hover:border-primary"
            onClick={() => handleOauthClick("google", origin)}
            disabled={isLoading}
          >
            Google
          </Button>
          <Button
            variant="outline"
            type="button"
            className="w-full hover:border hover:border-primary dark:hover:border dark:hover:border-primary"
            onClick={() => handleOauthClick("github", origin)}
            disabled={isLoading}
          >
            GitHub
          </Button>
        </div>
      </form>
    </Form>
  );
}
