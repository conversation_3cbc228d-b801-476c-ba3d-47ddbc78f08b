import { Star } from "lucide-react";
import { cn } from "@/utils/utils";

interface StarRatingProps {
  rating: number;
  max?: number;
  size?: "sm" | "md" | "lg";
  show_value?: boolean;
}

export function StarRating({ rating, max = 5, size = "md", show_value = true }: StarRatingProps) {
  const stars = Array.from({ length: max }, (_, i) => i + 1);

  const sizeClasses = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5",
  };

  return (
    <div className="flex items-center">
      <div className="flex">
        {stars.map((star) => (
          <Star
            key={star}
            className={cn(
              sizeClasses[size],
              "mr-0.5",
              star <= rating
                ? "text-yellow-400 fill-yellow-400"
                : star <= rating + 0.5
                  ? "text-yellow-400 fill-yellow-400/50"
                  : "text-gray-600",
            )}
          />
        ))}
      </div>
      {show_value && <span className="ml-2 text-sm text-gray-400">{rating.toFixed(1)}</span>}
    </div>
  );
}
