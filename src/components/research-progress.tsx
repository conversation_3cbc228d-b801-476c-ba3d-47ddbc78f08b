"use client";

import { motion, AnimatePresence } from "motion/react";
import { 
  Search, 
  Globe, 
  FileText, 
  Navigation, 
  Zap, 
  Download, 
  Archive, 
  Brain,
  CheckCircle,
  Loader2
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface ResearchStep {
  id: string;
  tool: string;
  title: string;
  description: string;
  details?: string;
  status: "active" | "completed" | "pending";
  timestamp?: Date;
}

interface ResearchProgressProps {
  currentStep: ResearchStep | null;
  completedSteps: ResearchStep[];
  isResearchComplete: boolean;
}

const getToolIcon = (tool: string) => {
  switch (tool) {
    case "web_search":
      return Search;
    case "visit_page":
      return Globe;
    case "page_navigation":
      return Navigation;
    case "find_on_page":
      return FileText;
    case "find_next":
      return Zap;
    case "reasoning_tool":
      return Brain;
    case "create_query":
      return Search;
    case "download_file":
      return Download;
    case "find_archived_url":
      return Archive;
    default:
      return FileText;
  }
};

const getToolColor = (tool: string) => {
  switch (tool) {
    case "web_search":
      return "bg-blue-500/10 text-blue-600 border-blue-500/20";
    case "visit_page":
      return "bg-green-500/10 text-green-600 border-green-500/20";
    case "page_navigation":
      return "bg-purple-500/10 text-purple-600 border-purple-500/20";
    case "find_on_page":
      return "bg-orange-500/10 text-orange-600 border-orange-500/20";
    case "find_next":
      return "bg-yellow-500/10 text-yellow-600 border-yellow-500/20";
    case "reasoning_tool":
      return "bg-pink-500/10 text-pink-600 border-pink-500/20";
    case "create_query":
      return "bg-indigo-500/10 text-indigo-600 border-indigo-500/20";
    case "download_file":
      return "bg-cyan-500/10 text-cyan-600 border-cyan-500/20";
    case "find_archived_url":
      return "bg-gray-500/10 text-gray-600 border-gray-500/20";
    default:
      return "bg-primary/10 text-primary border-primary/20";
  }
};

export function ResearchProgress({
  currentStep,
  completedSteps,
  isResearchComplete
}: ResearchProgressProps) {
  if (!currentStep && completedSteps.length === 0 && !isResearchComplete) {
    return null;
  }

  return (
    <Card className="mb-4 border-primary/20 bg-gradient-to-r from-primary/5 to-transparent">
      <CardContent className="p-4">
        <div className="flex items-center gap-2 mb-3">
          <Brain className="h-5 w-5 text-primary" />
          <h3 className="font-semibold text-foreground">Research Progress</h3>
          {isResearchComplete && (
            <Badge className="bg-green-500/10 text-green-600 border-green-500/20">
              <CheckCircle className="h-3 w-3 mr-1" />
              Complete
            </Badge>
          )}
        </div>

        <div className="space-y-3">
          {/* Completed Steps */}
          {completedSteps.map((step) => {
            const Icon = getToolIcon(step.tool);
            return (
              <motion.div
                key={step.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="flex items-start gap-3 p-3 rounded-lg bg-muted/30 border border-border/50"
              >
                <div className={`p-2 rounded-full ${getToolColor(step.tool)}`}>
                  <Icon className="h-4 w-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium text-sm text-foreground">{step.title}</h4>
                    <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">{step.description}</p>
                  {step.details && (
                    <p className="text-xs text-muted-foreground mt-1 italic">{step.details}</p>
                  )}
                </div>
              </motion.div>
            );
          })}

          {/* Current Step */}
          <AnimatePresence>
            {currentStep && (
              <motion.div
                key={currentStep.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                className="flex items-start gap-3 p-3 rounded-lg bg-primary/5 border border-primary/20"
              >
                <div className={`p-2 rounded-full ${getToolColor(currentStep.tool)}`}>
                  <Loader2 className="h-4 w-4 animate-spin" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium text-sm text-foreground">{currentStep.title}</h4>
                    <Badge variant="outline" className="text-xs">
                      In Progress
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">{currentStep.description}</p>
                  {currentStep.details && (
                    <p className="text-xs text-muted-foreground mt-1 italic">{currentStep.details}</p>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Progress Summary */}
          {(completedSteps.length > 0 || currentStep) && (
            <div className="flex items-center justify-between pt-2 border-t border-border/50">
              <span className="text-xs text-muted-foreground">
                {completedSteps.length} steps completed
                {currentStep && " • 1 in progress"}
              </span>
              {!isResearchComplete && (
                <div className="flex items-center gap-1">
                  <Loader2 className="h-3 w-3 animate-spin text-primary" />
                  <span className="text-xs text-primary">Researching...</span>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export type { ResearchStep, ResearchProgressProps };
