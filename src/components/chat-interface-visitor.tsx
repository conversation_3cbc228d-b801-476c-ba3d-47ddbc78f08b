"use client";

import type React from "react";
import { useState, useRef, useEffect } from "react";
import { useChat } from "@ai-sdk/react";
import { generateId, Message } from "ai";
import { isLocalStorageAvailable, saveToLocalStorage } from "@/lib/local-storage";
import { VisitorChat, ResearchStep } from "@/lib/types";
import { useChatToggle } from "@/lib/providers/chat-toggle-provider";
import { ResearchProgress } from "@/components/research-progress";
import { MarkdownRenderer } from "@/components/markdown-renderer";
import { Button } from "@/components/ui/button";
import TextareaAutosize from "react-textarea-autosize";
import {
  Send,
  Bot,
  User,
  Sparkles,
  X,
  MessageCircle,
  Maximize2,
  ChevronDown,
  ChevronUp,
  RefreshCcw,
  Microscope,
} from "lucide-react";
import { cn } from "@/utils/utils";
import { motion, AnimatePresence } from "motion/react";
import Link from "next/link";
import { Too<PERSON><PERSON>, Toolt<PERSON><PERSON>ontent, TooltipTrigger } from "./ui/tooltip";

interface Prop {
  visitorChat: VisitorChat | null;
  setVisitorChat: React.Dispatch<React.SetStateAction<VisitorChat | null>>;
}

export function VisitorChatInterface({ visitorChat, setVisitorChat }: Prop) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { isClosed, setIsClosed } = useChatToggle();
  const [chatSize, setChatSize] = useState({ width: 450, height: 500 });
  const [isResizing, setIsResizing] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const chatRef = useRef<HTMLDivElement>(null);
  const sizeRef = useRef({ width: chatSize.width, height: chatSize.height });
  const rafIdRef = useRef<number | null>(null);
  const userMessageRef = useRef<Message | null>(null);
  const [chatId, setChatId] = useState(() => visitorChat?.chat_id || generateId());
  const [deepResearch, setDeepResearch] = useState(false);

  // Research progress state
  const [currentResearchStep, setCurrentResearchStep] = useState<ResearchStep | null>(null);
  const [completedResearchSteps, setCompletedResearchSteps] = useState<ResearchStep[]>([]);
  const [isResearchComplete, setIsResearchComplete] = useState(false);
  const { id, messages, input, status, setInput, setMessages, handleInputChange, handleSubmit } =
    useChat({
      id: chatId,
      api: "/api/visitors-chat",
      initialMessages: visitorChat?.messages || [],
      experimental_prepareRequestBody({ messages, id }) {
        return { messages, id, deepResearch };
      },
      onFinish: (message, finishReason) => {
        if (finishReason.finishReason === "stop" && userMessageRef.current) {
          const now = new Date();
          const updatedMessageArray = visitorChat
            ? visitorChat.messages.concat(userMessageRef.current, message)
            : [userMessageRef.current, message];
          const newVisitorChat: VisitorChat = {
            chat_id: id,
            messages: updatedMessageArray,
            created_at: visitorChat?.created_at || now,
            updated_at: now,
          };

          setVisitorChat(newVisitorChat);

          if (isLocalStorageAvailable()) {
            saveToLocalStorage("visitorChat", newVisitorChat);
          }
          userMessageRef.current = null;
        }
      },
    });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth", block: "end" });
  };

  useEffect(() => {
    if (isExpanded) {
      scrollToBottom();
    }
  }, [messages, isExpanded]);

  const toggleExpand = () => {
    if (isExpanded) {
      setIsExpanded(false);
    } else {
      setIsExpanded(true);
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  };

  const toggleMinimize = () => {
    setIsClosed(!isClosed);
  };

  useEffect(() => {
    if (!isClosed) {
      setIsExpanded(true);
      setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
    }
  }, [isClosed]);

  const handleResizeMouseDown = (e: React.MouseEvent) => {
    if (!isExpanded) return;
    e.stopPropagation();
    e.preventDefault();

    setIsResizing(true);
    document.body.style.userSelect = "none";

    const startX = e.clientX;
    const startY = e.clientY;
    const startWidth = sizeRef.current.width;
    const startHeight = sizeRef.current.height;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;
      const newW = Math.max(450, Math.min(800, startWidth - deltaX));
      const newH = Math.max(500, Math.min(760, startHeight - deltaY));

      // update the ref
      sizeRef.current = { width: newW, height: newH };

      if (rafIdRef.current === null) {
        rafIdRef.current = window.requestAnimationFrame(() => {
          if (chatRef.current) {
            chatRef.current.style.width = `${sizeRef.current.width}px`;
            chatRef.current.style.height = `${sizeRef.current.height}px`;
          }
          rafIdRef.current = null;
        });
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.body.style.userSelect = "";
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);

      // Commit final size into React state
      setChatSize({ ...sizeRef.current });

      if (rafIdRef.current !== null) {
        window.cancelAnimationFrame(rafIdRef.current);
        rafIdRef.current = null;
      }
    };

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  };

  const handleChatSubmit = (
    e: React.FormEvent<HTMLFormElement> | React.KeyboardEvent<HTMLTextAreaElement>,
  ) => {
    e.preventDefault();

    if (!input.trim()) return;

    const userText: Message = {
      id: generateId(),
      role: "user",
      content: input,
      createdAt: new Date(),
      parts: [{ type: "text", text: input }],
    };

    userMessageRef.current = userText;
    handleSubmit(e);
    setTimeout(() => inputRef.current?.focus(), 50);
  };

  const clearChat = () => {
    setMessages([]);
    setVisitorChat(null);
    setChatId(generateId());
    if (isLocalStorageAvailable()) {
      localStorage.removeItem("visitorChat");
    }
  };

  // Simulate research progress for demonstration
  const simulateResearchProgress = () => {
    const researchSteps: Omit<ResearchStep, "status">[] = [
      {
        id: "step-1",
        tool: "web_search",
        title: "Initiating web search",
        description: "Searching for MCP server information",
        details: "Finding relevant documentation and repositories"
      },
      {
        id: "step-2",
        tool: "visit_page",
        title: "Analyzing documentation",
        description: "Reading official MCP server guides",
        details: "Extracting technical specifications and features"
      },
      {
        id: "step-3",
        tool: "find_on_page",
        title: "Extracting key information",
        description: "Finding specific server capabilities",
        details: "Identifying integration patterns and examples"
      },
      {
        id: "step-4",
        tool: "reasoning_tool",
        title: "Synthesizing findings",
        description: "Analyzing collected information",
        details: "Preparing comprehensive recommendations"
      }
    ];

    let stepIndex = 0;
    let timeoutIds: NodeJS.Timeout[] = [];

    const processNextStep = () => {
      if (stepIndex < researchSteps.length) {
        const step = researchSteps[stepIndex];
        setCurrentResearchStep({ ...step, status: "active", timestamp: new Date() });

        const timeoutId = setTimeout(() => {
          setCompletedResearchSteps(prev => [...prev, { ...step, status: "completed", timestamp: new Date() }]);
          setCurrentResearchStep(null);
          stepIndex++;

          if (stepIndex < researchSteps.length) {
            const nextTimeoutId = setTimeout(processNextStep, 800);
            timeoutIds.push(nextTimeoutId);
          }
        }, 1500 + Math.random() * 1500); // Random delay between 1.5-3 seconds

        timeoutIds.push(timeoutId);
      }
    };

    const initialTimeoutId = setTimeout(processNextStep, 1000);
    timeoutIds.push(initialTimeoutId);

    // Cleanup function to clear timeouts if component unmounts
    return () => {
      timeoutIds.forEach(id => clearTimeout(id));
    };
  };

  // Reset research progress when starting new deep research
  useEffect(() => {
    if (status === "submitted" && deepResearch) {
      setCurrentResearchStep(null);
      setCompletedResearchSteps([]);
      setIsResearchComplete(false);

      // Simulate research progress steps
      const cleanup = simulateResearchProgress();

      // Return cleanup function
      return cleanup;
    }
  }, [status, deepResearch]);

  // Handle research completion when status changes to ready
  useEffect(() => {
    if (status === "ready" && deepResearch && currentResearchStep) {
      // Move current step to completed
      setCompletedResearchSteps(prev => [...prev, { ...currentResearchStep, status: "completed" }]);
      setCurrentResearchStep(null);
      setIsResearchComplete(true);
    }
  }, [status, deepResearch, currentResearchStep]);

  return (
    <>
      <div id="chat-interface" className={cn("fixed z-50 bottom-4 right-4")}>
        <AnimatePresence mode="wait">
          {/* Minimized floating chat bubble */}
          {isClosed && (
            <motion.div
              key="bubble"
              initial={{ scale: 0, opacity: 0, transition: { duration: 0.1 } }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0, transition: { duration: 0.1 } }}
              className="relative group"
            >
              {/* Main chat button */}
              <Tooltip>
                <TooltipTrigger>
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className="relative w-16 h-16 bg-gradient-to-r from-teal via-amber to-crimson rounded-full flex items-center justify-center cursor-pointer shadow-[0px_0px_1px_0px_var(--primary)] hover:shadow-[0px_0px_20px_0px_var(--primary)] transition-all duration-300"
                    onClick={toggleMinimize}
                  >
                    <MessageCircle className="h-8 w-8 text-primary" />
                  </motion.div>
                </TooltipTrigger>
                <TooltipContent
                  className="bg-background/95 backdrop-blur-sm border border-primary/50 rounded-lg text-sm text-foreground relative z-100"
                  arrowClassName="fill-background bg-background rounded-[2px] z-10"
                >
                  <p>Chat with AI Assistant</p>
                </TooltipContent>
              </Tooltip>
            </motion.div>
          )}

          {/* Main chat interface */}
          {!isClosed && (
            <motion.div
              ref={chatRef}
              key="chat"
              initial={{ scale: [0.2, 0.4, 0.8], opacity: 0, transition: { duration: 0.1 } }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: [0.8, 0.4, 0.2], opacity: 0, transition: { duration: 0.1 } }}
              className={cn(
                "shadow-xl overflow-hidden",
                isResizing ? "" : "transition-shadow transition-border duration-500 ease-in-out",
                "border border-primary/50 bg-background/100 dark:bg-background/70 backdrop-blur-sm relative",
                isExpanded
                  ? "rounded-2xl flex flex-col"
                  : "w-[450px] h-37 rounded-2xl flex flex-col cursor-pointer hover:shadow-2xl hover:border-primary/70",
              )}
              style={isExpanded ? { width: chatSize.width, height: chatSize.height } : undefined}
              onClick={!isExpanded ? toggleExpand : undefined}
            >
              {/* Resize handle */}
              {isExpanded && (
                <div
                  className="absolute top-0 left-0 w-4 h-4 cursor-nw-resize opacity-50 hover:opacity-100 transition-opacity z-10"
                  onMouseDown={handleResizeMouseDown}
                >
                  <Maximize2 className="h-3 w-3 text-primary" />
                </div>
              )}
              {/* Header */}
              <div
                className={cn(
                  "flex justify-between items-center",
                  isExpanded ? "p-4 border-b" : "p-4",
                )}
              >
                <div className="flex items-center gap-3 flex-1">
                  <div
                    className={cn(
                      "rounded-full flex items-center justify-center",
                      isExpanded
                        ? "w-10 h-10"
                        : "w-12 h-12 bg-gradient-to-r from-teal via-amber to-crimson shadow-lg",
                    )}
                  >
                    <Bot
                      className={cn(
                        "text-foreground",
                        isExpanded ? "h-5 w-5" : "h-6 w-6 text-white",
                      )}
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <h2
                          className={cn(
                            "font-bold text-primary",
                            isExpanded ? "text-lg" : "text-lg",
                          )}
                        >
                          MCP Assistant
                        </h2>
                        {isExpanded ? (
                          <div className="flex items-center gap-2 mt-1">
                            {!visitorChat?.messages && (
                              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            )}
                            <span className="text-xs text-foreground/80 font-medium">
                              {visitorChat?.messages ? (
                                <>
                                  <Link href="/auth/login" className="text-primary">
                                    Log In
                                  </Link>{" "}
                                  to save this conversation!
                                </>
                              ) : (
                                "Hello there!"
                              )}
                            </span>
                          </div>
                        ) : (
                          // Online indicator when collapsed
                          <div className="flex items-center gap-2 mt-1">
                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span className="text-xs text-foreground/80 font-medium">
                              Hello there!
                            </span>
                          </div>
                        )}
                      </div>
                      {!isExpanded && (
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={toggleMinimize}
                            className="text-primary/50 hover:text-primary hover:bg-transparent dark:hover:bg-transparent"
                            title="Close"
                          >
                            <X strokeWidth={3} className="h-4 w-4" />
                          </Button>
                          {/* Up oscillating chevron when collapsed */}
                          <motion.div
                            animate={{ y: [0, -4, 0] }}
                            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                            className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 hover:bg-primary/20 transition-colors"
                            onClick={toggleExpand}
                          >
                            <Button
                              variant="ghost"
                              className="rounded-full hover:bg-transparent dark:hover:bg-transparent"
                            >
                              <ChevronUp className="h-4 w-4 text-primary" />
                            </Button>
                          </motion.div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {isExpanded && (
                  // Control buttons
                  <div className="flex flex-row">
                    {/* Clearing the chat */}
                    <Tooltip>
                      <TooltipTrigger>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={clearChat}
                          className="text-primary/50 hover:text-primary hover:bg-transparent dark:hover:bg-transparent"
                        >
                          <RefreshCcw />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Clear Chat</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Collapsing the floating chat */}
                    <Tooltip>
                      <TooltipTrigger>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={toggleExpand}
                          className="text-primary/50 hover:text-primary hover:bg-transparent dark:hover:bg-transparent"
                        >
                          <ChevronDown />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Minimize</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Close to chat-bubble */}
                    <Tooltip>
                      <TooltipTrigger>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={toggleMinimize}
                          className="text-primary/50 hover:text-primary hover:bg-transparent dark:hover:bg-transparent"
                        >
                          <X strokeWidth={3} className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Close</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                )}
              </div>

              {/* Research Progress Component - Fixed at top */}
              {deepResearch && isExpanded && (
                <div className="px-4 pt-2">
                  <ResearchProgress
                    currentStep={currentResearchStep}
                    completedSteps={completedResearchSteps}
                    isResearchComplete={isResearchComplete}
                  />
                </div>
              )}

              {/* Message Pannel */}
              <AnimatePresence>
                {isExpanded && (
                  <div className="p-4 flex-grow overflow-y-auto">
                    {messages.length > 0 ? (
                      // Message pannel with messages
                      <div className="space-y-4">
                        {messages.map((message) => (
                          <motion.div
                            key={message.id}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3 }}
                            className={cn(
                              "flex",
                              message.role === "user" ? "justify-end" : "justify-start",
                            )}
                          >
                            <div className="flex items-start max-w-[85%] gap-2">
                              {message.role === "assistant" && (
                                <div className="w-8 h-8 rounded-full flex-shrink-0 flex items-center justify-center mt-1">
                                  <Bot className="h-4 w-4 text-foreground" />
                                </div>
                              )}
                              <div
                                className={cn(
                                  "rounded-xl px-3 py-2 shadow-sm",
                                  message.role === "user"
                                    ? "bg-primary text-background"
                                    : "bg-background border border-foreground/20",
                                )}
                              >
                                {message.role === "assistant" ? (
                                  <MarkdownRenderer
                                    content={message.content}
                                    className="text-sm"
                                  />
                                ) : (
                                  <p className="leading-relaxed text-sm">{message.content}</p>
                                )}
                                <p className="text-xs opacity-70 mt-1 text-right">
                                  {message.createdAt?.toLocaleTimeString([], {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                  })}
                                </p>
                              </div>
                              {message.role === "user" && (
                                <div className="w-8 h-8 rounded-full bg-primary flex-shrink-0 flex items-center justify-center mt-1">
                                  <User className="h-4 w-4 text-background" />
                                </div>
                              )}
                              {status === "submitted" && (
                                <div className="mr-2 flex flex-row gap-4 items-center h-fit">
                                  <div className="w-8 h-8 rounded-full flex-shrink-0 flex items-center justify-center mt-1">
                                    <Bot className="h-4 w-4 text-foreground" />
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <motion.div
                                      className="w-1 h-1 bg-primary/80 rounded-full"
                                      animate={{ opacity: [0.6, 1, 0.6] }}
                                      transition={{ duration: 1.5, repeat: Infinity, delay: 0 }}
                                    ></motion.div>

                                    <motion.div
                                      className="w-1 h-1 bg-primary/60 rounded-full"
                                      animate={{ opacity: [0.4, 1, 0.4] }}
                                      transition={{ duration: 1.5, repeat: Infinity, delay: 0.5 }}
                                    ></motion.div>

                                    <motion.div
                                      className="w-1 h-1 bg-primary/40 rounded-full"
                                      animate={{ opacity: [0.2, 1, 0.2] }}
                                      transition={{ duration: 1.5, repeat: Infinity, delay: 1 }}
                                    ></motion.div>
                                  </div>
                                </div>
                              )}
                            </div>
                          </motion.div>
                        ))}
                        <div ref={messagesEndRef} />
                      </div>
                    ) : (
                      // Message pannel without messages
                      <div className="h-full flex flex-col items-center justify-center text-center p-2">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-r from-teal via-amber to-crimson flex items-center justify-center mb-4">
                          <Sparkles className="h-6 w-6 text-foreground" />
                        </div>
                        <h3 className="text-lg font-bold mb-2 gradient-text">How can I help?</h3>
                        <p className="text-foreground text-sm mb-4">
                          Ask me anything about MCP servers
                        </p>
                        <div className="grid grid-cols-1 gap-2 w-full">
                          {["Best MCP servers for IoT?", "How to integrate QuantumMCP?"].map(
                            (suggestion, i) => (
                              <Button
                                key={i}
                                variant="outline"
                                className="text-xs dark:bg-transparent h-auto py-2 px-3 hover:border-none hover:shadow-primary hover:shadow-xs"
                                onClick={() => {
                                  setInput(suggestion);
                                  inputRef.current?.focus();
                                }}
                              >
                                {suggestion}
                              </Button>
                            ),
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </AnimatePresence>

              {/* Text area */}
              <form onSubmit={handleChatSubmit} className="p-3 border-t flex gap-2">
                <div className="flex flex-row w-full items-center gap-2">
                  <Tooltip>
                    <TooltipTrigger>
                      <Button
                        size={"icon"}
                        variant={deepResearch ? "default" : "ghost"}
                        onClick={() => setDeepResearch(!deepResearch)}
                        disabled={status !== "ready"}
                        className={cn(
                          !deepResearch
                            ? "hover:text-primary hover:border hover:border-primary hover:dark:border-primary border border-foreground/20"
                            : "border border-foreground/20",
                        )}
                      >
                        <Microscope />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Deep Research</p>
                    </TooltipContent>
                  </Tooltip>
                  <TextareaAutosize
                    ref={inputRef}
                    data-chat-input
                    placeholder="Ask me anything about MCP servers..."
                    disabled={status !== "ready"}
                    className={cn(
                      "placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border border-primary/40 bg-transparent px-3 py-2 text-sm transition-[color,box-shadow] outline-none disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 resize-none focus-within:outline-none",
                      "focus-visible:ring-[1.25px] focus-visible:ring-primary/40",
                      "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
                    )}
                    minRows={1}
                    maxRows={3}
                    spellCheck={false}
                    autoComplete="off"
                    autoCorrect="off"
                    name="message"
                    rows={1}
                    required
                    value={input}
                    onChange={handleInputChange}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        if (input.trim()) {
                          handleChatSubmit(e as React.KeyboardEvent<HTMLTextAreaElement>);
                        }
                      }
                    }}
                  />
                </div>
                <Button
                  type="submit"
                  size="icon"
                  disabled={!input || status !== "ready"}
                  className={`bg-transparent border border-primary/40 transition-opacity h-10 w-10 ${input && "ring-[1.25px] ring-primary/40"}`}
                >
                  <Send className="h-4 w-4 text-foreground" />
                </Button>
              </form>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  );
}
