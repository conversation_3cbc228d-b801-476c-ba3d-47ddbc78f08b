"use client";

import { useState } from "react";
import MCPServerCard from "@/components/mcp-server-card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, Filter } from "lucide-react";
import { getMcpServers } from "@/lib/data";
import { motion } from "motion/react";

export default function McpServerList() {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("rating");

  const servers = getMcpServers();

  const filteredServers = servers.filter(
    (server) =>
      server.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      server.description.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const sortedServers = [...filteredServers].sort((a, b) => {
    if (sortBy === "rating") {
      return b.rating - a.rating;
    } else if (sortBy === "name") {
      return a.name.localeCompare(b.name);
    } else if (sortBy === "newest") {
      return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime();
    }
    return 0;
  });

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  return (
    <div id="server-list">
      <div className="relative max-w-3xl mx-auto mb-20">
        <div className="absolute -inset-1 from-primary via-teal to-amber rounded-2xl blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-500"></div>
        <div className="relative bg-background/80 backdrop-blur-xl border border-primary/20 rounded-2xl shadow-2xl overflow-hidden">
          <div className="flex flex-col md:flex-row">
            {/* Search Section */}
            <div className="flex-1 relative">
              <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-primary h-5 w-5" />
              <Input
                placeholder="Search MCP servers by name, description, or tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-14 pr-6 py-6 border-0 bg-transparent focus-visible:ring-0 text-base placeholder:text-foreground/50 rounded-2xl"
              />
            </div>

            {/* Divider */}
            <div className="hidden md:block w-px bg-primary/20"></div>

            {/* Filter Section */}
            <div className="flex items-center px-6 py-4 md:py-0 border-t md:border-t-0 border-primary/20">
              <Filter className="text-primary h-4 w-4 mr-3" />
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="border-0 bg-transparent focus:ring-0 w-44 text-foreground">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent className="bg-background/95 backdrop-blur-xl border border-primary/20">
                  <SelectItem value="rating" className="hover:bg-primary/10">
                    <div className="flex items-center gap-2">
                      <svg
                        className="w-4 h-4 text-amber-500"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      Highest Rated
                    </div>
                  </SelectItem>
                  <SelectItem value="name" className="hover:bg-primary/10">
                    <div className="flex items-center gap-2">
                      <svg
                        className="w-4 h-4 text-blue-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                        />
                      </svg>
                      Name (A-Z)
                    </div>
                  </SelectItem>
                  <SelectItem value="newest" className="hover:bg-primary/10">
                    <div className="flex items-center gap-2">
                      <svg
                        className="w-4 h-4 text-green-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      Newest First
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Search stats */}
        <div className="flex items-center justify-center mt-6 text-sm text-foreground/60">
          <span>
            {filteredServers.length} server{filteredServers.length !== 1 ? "s" : ""} found
            {searchQuery && ` for "${searchQuery}"`}
          </span>
        </div>
      </div>

      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        variants={container}
        initial="hidden"
        animate="show"
      >
        {sortedServers.map((server) => (
          <motion.div key={server.id} variants={item}>
            <MCPServerCard server={server} />
          </motion.div>
        ))}
      </motion.div>

      {sortedServers.length === 0 && (
        <div className="text-center py-16">
          <p className="text-2xl text-foreground ">No servers found matching your search.</p>
        </div>
      )}
    </div>
  );
}
