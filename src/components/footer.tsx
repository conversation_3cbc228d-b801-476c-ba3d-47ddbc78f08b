import Link from "next/link";
import Image from "next/image";
import { Mail } from "lucide-react";
import { RiGith<PERSON>Line, R<PERSON><PERSON><PERSON>edinFill, RiTwitterXLine } from "react-icons/ri";

export default function Footer() {
  return (
    <footer className="border-t border-gray-800 bg-background">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <Link href="/" className="flex items-center space-x-2 group">
              <Image
                src="/lightray-tech.png"
                alt="Lightray Tech Logo Dark"
                width={20}
                height={20}
                className="hidden dark:block"
              />
              <Image
                src="/lightray-tech-light.png"
                alt="Lightray Tech Logo Light"
                width={20}
                height={20}
                className="block dark:hidden"
              />
              <span className="text-xl font-bold text-foreground">MCP Marketplace</span>
            </Link>
            <p className="mt-4 text-sm text-gray-600">
              The premier destination for discovering and integrating Model Context Protocol
              servers.
            </p>
            <div className="mt-6 flex space-x-4">
              <a href="#" className="text-gray-600 hover:text-primary">
                <RiGithubLine className="h-5 w-5" />
                <span className="sr-only">GitHub</span>
              </a>
              <a href="#" className="text-gray-600 hover:text-primary">
                <RiTwitterXLine className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </a>
              <a href="#" className="text-gray-600 hover:text-primary">
                <RiLinkedinFill className="h-5 w-5" />
                <span className="sr-only">LinkedIn</span>
              </a>
              <a href="#" className="text-gray-600 hover:text-primary">
                <Mail className="h-5 w-5" />
                <span className="sr-only">Email</span>
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-600 hover:text-primary">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-600 hover:text-primary">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-600 hover:text-primary">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* <div>
            <h3 className="text-lg font-semibold mb-4">Resources</h3>
            <ul className="space-y-2">
              <li>
          <a href="#" className="text-gray-400 hover:text-primary">
            Documentation
          </a>
              </li>
              <li>
          <a href="#" className="text-gray-400 hover:text-primary">
            API Reference
          </a>
              </li>
              <li>
          <a href="#" className="text-gray-400 hover:text-primary">
            Blog
          </a>
              </li>
              <li>
          <a href="#" className="text-gray-400 hover:text-primary">
            Tutorials
          </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Legal</h3>
            <ul className="space-y-2">
              <li>
          <a href="#" className="text-gray-400 hover:text-primary">
            Terms of Service
          </a>
              </li>
              <li>
          <a href="#" className="text-gray-400 hover:text-primary">
            Privacy Policy
          </a>
              </li>
              <li>
          <a href="#" className="text-gray-400 hover:text-primary">
            Cookie Policy
          </a>
              </li>
            </ul>
          </div> */}
        </div>

        <div className="mt-12 border-t border-gray-800 pt-8 text-center">
          <p className="text-sm text-gray-600">
            © {new Date().getFullYear()} Lightray Technologies. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
