"use client";

import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuPortal,
  DropdownMenuSubContent,
} from "@/components/ui/dropdown-menu";
import { Sun, Moon, Monitor, LogOut } from "lucide-react";
import { logOut } from "@/lib/supabase-server-actions";
import { Dispatch, SetStateAction } from "react";

interface AvatarMenuProps {
  initials: string;
  theme: string | undefined;
  setTheme: Dispatch<SetStateAction<string>>;
  // user: User;
  // setUser: Dispatch<SetStateAction<User | null>>;
}

const AvatarMenu = ({ initials, theme, setTheme }: AvatarMenuProps) => {
  const handleSelect = async () => {
    await logOut();
  };

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Avatar className="cursor-pointer size-9 shadow-[0px_0px_2px_0px_var(--primary)]">
            <AvatarFallback>{initials}</AvatarFallback>
          </Avatar>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          side="bottom"
          avoidCollisions={true}
          collisionPadding={{ top: 30, right: 65 }}
          sticky="always"
        >
          <DropdownMenuSub>
            <DropdownMenuSubTrigger className="flex row gap-2 items-center cursor-pointer">
              {theme == "dark" ? (
                <Moon className="rotate-0 scale-100 transition-all" />
              ) : theme == "light" ? (
                <Sun className="rotate-0 scale-100 transition-all" />
              ) : (
                <Monitor className="rotate-0 scale-100 transition-all" />
              )}
              Theme
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent sideOffset={3}>
                <DropdownMenuItem onClick={() => setTheme("dark")} className="cursor-pointer">
                  Dark
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTheme("light")} className="cursor-pointer">
                  Light
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTheme("system")} className="cursor-pointer">
                  System
                </DropdownMenuItem>
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onSelect={handleSelect}
            className="flex row gap-2 items-center cursor-pointer"
          >
            <LogOut />
            Log Out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};

export default AvatarMenu;
