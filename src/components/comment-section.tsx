"use client";

import Link from "next/link";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import TextareaAutosize from "react-textarea-autosize";
import Rating from "@mui/material/Rating";
import StarBorderIcon from "@mui/icons-material/StarBorder";
import { cn, ratingLabels } from "@/utils/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { getComments } from "@/lib/data";
import type { CommentType } from "@/lib/types";
import { toast } from "sonner";

interface CommentSectionProps {
  server_id: string;
}

export function CommentSection({ server_id }: CommentSectionProps) {
  const [comments, setComments] = useState<CommentType[]>([]);
  const [newComment, setNewComment] = useState("");
  const [isLoggedIn, setIsLoggedIn] = useState(true);
  const [value, setValue] = useState<number | null>(0);
  const [hover, setHover] = useState(-1);

  useEffect(() => {
    // Fetch comments for this server
    const fetchedComments = getComments(server_id);
    setComments(fetchedComments);

    // Check if user is logged in (mock)
    setIsLoggedIn(true); // Set to false for demo purposes
  }, [server_id]);

  const getLabelText = (value: number) => {
    return `${value} Star${value !== 1 ? "s" : ""}, ${ratingLabels[value]}`;
  };

  const handleSubmitComment = () => {
    if (!newComment.trim()) return;

    // In a real app, this would send the comment to an API
    toast.warning("Login Required", { description: "Please log in to post comments." });
  };

  return (
    <div className="bg-secondary dark:bg-card rounded-2xl p-8">
      <h2 className="text-4xl text-primary text-center font-bold mb-6">Comments & Reviews</h2>

      {isLoggedIn ? (
        <div className="mb-8 border border-primary/50 rounded-xl p-6">
          <h3 className="text-lg font-semibold mb-4">Write a Review</h3>
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Rating</label>
            <div className="flex flex-row items-center gap-2">
              <Rating
                name={`user-id_${server_id}_rating`}
                value={value}
                size="medium"
                precision={0.5}
                emptyIcon={<StarBorderIcon fontSize="inherit" className="text-foreground/50" />}
                getLabelText={getLabelText}
                onChange={(event, new_value) => {
                  setValue(new_value ?? 0);
                }}
                onChangeActive={(event, new_hover) => {
                  setHover(new_hover);
                }}
              />
              {value !== null && (
                <div>
                  {hover !== -1 ? `${hover} -` : value == 0 ? value : `${value} -`}{" "}
                  {ratingLabels[hover !== -1 ? hover : value]}
                </div>
              )}
            </div>
          </div>
          <div className="mb-4">
            <label htmlFor="comment" className="block text-sm font-medium mb-2">
              Your Comment
            </label>
            <TextareaAutosize
              placeholder="Share your experience with this MCP server..."
              className={cn(
                "placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground border border-foreground/20 flex  min-h-16 w-full min-w-0 rounded-md bg-transparent  dark:bg-input/30 px-3 py-2 text-sm transition-[color,box-shadow] outline-none file:inline-flex file:h-7 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50  resize-none focus-within:outline-none",
                "focus-visible:ring-[3px] focus-visible:border-none focus-visible:ring-primary/75 disabled:cursor-not-allowed disabled:opacity-50",
              )}
              // onKeyDown={onKeyDown}
              id="comment"
              minRows={4}
              maxRows={4}
              autoFocus
              spellCheck={false}
              autoComplete="off"
              autoCorrect="off"
              name="message"
              rows={1}
              required
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
            />
          </div>
          <Button className="text-white" onClick={handleSubmitComment}>
            Submit Review
          </Button>
        </div>
      ) : (
        <div className="mb-8 border border-input rounded-xl p-6 bg-card dark:bg-secondary">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">Join the Conversation</h3>
            <p className="mb-4">Log in to share your experience with this MCP server.</p>
            <div className="flex justify-center gap-4">
              <Button
                asChild
                variant="secondary"
                className="border border-input hover:text-primary"
              >
                <Link href="/auth/login">Log In</Link>
              </Button>
              <Button
                asChild
                variant="secondary"
                className="border border-input hover:text-primary"
              >
                <Link href="/auth/registration">Create Account</Link>
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-6">
        {comments.length > 0 ? (
          comments.map((comment) => (
            <div key={comment.id} className="border-b border-gray-700 pb-6 last:border-0">
              <div className="flex items-start gap-4">
                <Avatar>
                  <AvatarImage
                    src={comment.user.avatar || "/placeholder.svg"}
                    alt={comment.user.name}
                  />
                  <AvatarFallback>{comment.user.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex justify-between items-center mb-1">
                    <h4 className="font-semibold">{comment.user.name}</h4>
                    <span className="text-sm text-foreground/70">
                      {new Date(comment.date).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex flex-row gap-2 items-center">
                    <Rating
                      name={`${comment.id}-rating`}
                      value={comment.rating}
                      size="small"
                      precision={0.5}
                      emptyIcon={
                        <StarBorderIcon fontSize="inherit" className="text-foreground/50" />
                      }
                      readOnly
                    />
                    <div>
                      {comment.rating} - {ratingLabels[comment.rating]}
                    </div>
                  </div>
                  <p className="mt-2 text-foreground">{comment.content}</p>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8">
            <p className="text-foreground/50">No comments yet. Be the first to review!</p>
          </div>
        )}
      </div>
    </div>
  );
}
