"use client";
import type React from "react";
import * as motion from "motion/react-client";

// Animated underline component
export function AnimatedUnderlineText({
  children,
  className = "",
  underlineColor,
}: {
  children: React.ReactNode;
  className?: string;
  underlineColor?: string;
}) {
  return (
    <motion.span
      className={`relative inline-block cursor-pointer ${className}`}
      initial="rest"
      whileHover="hover"
      animate="rest"
    >
      {children}
      <motion.span
        className="absolute bottom-0 left-0 right-0 h-0.5 rounded-full"
        style={{
          transformOrigin: "center",
          backgroundColor: underlineColor || "var(--hue-5)", // Default to cyan if no color provided
        }}
        variants={{
          rest: {
            scaleX: 0,
            opacity: 0,
            transition: {
              duration: 0.3,
              ease: "easeIn",
            },
          },
          hover: {
            scaleX: 1,
            opacity: 1,
            transition: {
              duration: 0.3,
              ease: "easeOut",
            },
          },
        }}
      />
    </motion.span>
  );
}
