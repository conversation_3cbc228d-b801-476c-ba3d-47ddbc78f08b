"use client";
import Image from "next/image";
import { useState, useEffect } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { usePathname } from "next/navigation";
import { Menu, X } from "lucide-react";
import { motion, AnimatePresence } from "motion/react";
import { ThemeToggle } from "@/components/theme-toggle";
import { useTheme } from "next-themes";
import { useUser } from "@/lib/providers/user-provider";
import { getInitials } from "@/utils/utils";
import AvatarMenu from "./avatar-menu";
import { AnimatedUnderlineText } from "@/components/animated-underline";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMounted, setIsMounted] = useState(false); // Add isMounted state
  const pathname = usePathname();
  const { theme, setTheme } = useTheme(); // Get the current theme
  const { user } = useUser();
  const initials = getInitials(user?.user_metadata.name);

  useEffect(() => {
    setIsMounted(true); // Set isMounted to true on client mount
  }, []);

  useEffect(() => {
    if (isMenuOpen) {
      document.body.classList.add("scroll-lock");
    } else {
      document.body.classList.remove("scroll-lock");
    }

    return () => {
      document.body.classList.remove("scroll-lock");
    };
  }, [isMenuOpen]);

  const navigation = [
    { name: "Home", href: "/" },
    { name: "Marketplace", href: "/marketplace" },
    { name: "About", href: "/about" },
    { name: "Contact", href: "/contact" },
  ];

  const isActive = (path: string) => {
    return pathname === path || (path !== "/" && pathname.includes(path));
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Determine logo based on theme
  const logoSrc = theme === "light" ? "/lightray-tech-light.png" : "/lightray-tech.png";
  const logoAlt = theme === "light" ? "Lightray Tech Logo (Light)" : "Lightray Tech Logo";

  return (
    <header
      className={`sticky top-0 z-50 w-full transition-all duration-500 ${
        isScrolled
          ? "bg-background/90 backdrop-blur-xl shadow-xl border-b border-primary/10"
          : "bg-transparent"
      } ${isMenuOpen ? "bg-background/95" : ""}`}
    >
      <div className="container mx-auto px-4">
        <div className="flex h-20 items-center justify-between">
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3 group">
              {isMounted && ( // Conditionally render Image only on client
                <div className="relative">
                  <div className="absolute inset-0 bg-primary/20 rounded-full blur-md group-hover:blur-lg transition-all duration-300"></div>
                  <Image
                    src={logoSrc} // Use the dynamic logo source
                    alt={logoAlt} // Use the dynamic alt text
                    width={32}
                    height={32}
                    className="relative z-10 group-hover:scale-110 transition-transform duration-300"
                  />
                </div>
              )}
              <span className="text-xl font-bold text-foreground group-hover:text-primary transition-colors duration-300">
                MCP Marketplace
              </span>
            </Link>
          </div>

          {/* Desktop navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) =>
              isActive(item.href) ? (
                <Link
                  key={item.name}
                  href={item.href}
                  className="px-1 py-1 font-medium transition-all duration-300 text-primary cursor-default"
                >
                  {item.name}
                </Link>
              ) : (
                <AnimatedUnderlineText
                  key={item.name}
                  underlineColor="var(--primary)"
                  className="px-1 py-1 font-medium transition-all duration-300"
                >
                  <Link key={item.name} href={item.href}>
                    {item.name}
                  </Link>
                </AnimatedUnderlineText>
              ),
            )}
          </nav>

          <div className="hidden md:flex items-center space-x-3">
            {user ? (
              <AvatarMenu initials={initials} theme={theme} setTheme={setTheme} />
            ) : (
              <div className="flex items-center space-x-3">
                {pathname === "/auth/login" ? (
                  <AnimatedUnderlineText
                    underlineColor="var(--primary)"
                    className="px-1 py-1 font-medium transition-all duration-300"
                  >
                    <Link href="/auth/registration">Register</Link>
                  </AnimatedUnderlineText>
                ) : (
                  <AnimatedUnderlineText
                    underlineColor="var(--primary)"
                    className="px-1 py-1 font-medium transition-all duration-300"
                  >
                    <Link href="/auth/login">Log In</Link>
                  </AnimatedUnderlineText>
                )}
                <div className="w-px h-6 bg-border"></div>
                <ThemeToggle />
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
              className="text-primary"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={false}
            animate={{ opacity: 1, height: 1 }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0 }}
            className="md:hidden"
          >
            <div className="mx-auto px-4 py-6 flex flex-col space-y-6 items-center bg-background/95 backdrop-blur-xs shadow-lg h-screen">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`block text-lg font-medium transition-colors ${
                    isActive(item.href) ? "text-primary" : "text-foreground"
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              <div className="pt-6 flex flex-col space-y-4 w-[15rem]">
                <Link href="/auth/login" onClick={() => setIsMenuOpen(false)}>
                  <Button variant="outline" className="w-full border-accent">
                    Sign In
                  </Button>
                </Link>
                <Link href="/auth/signup" onClick={() => setIsMenuOpen(false)}>
                  <Button variant="outline" className="w-full border-accent">
                    Sign Up
                  </Button>
                </Link>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}
