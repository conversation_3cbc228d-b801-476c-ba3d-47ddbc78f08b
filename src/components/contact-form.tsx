"use client";

import type React from "react";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import TextareaAutosize from "react-textarea-autosize";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { cn } from "@/utils/utils";

export function ContactForm() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      toast("Message Sent", {
        description: "We'll get back to you as soon as possible.",
      });

      setFormData({
        name: "",
        email: "",
        subject: "",
        message: "",
      });

      setIsSubmitting(false);
    }, 1500);
  };

  return (
    <div className="bg-secondary dark:bg-card border border-input rounded-2xl p-8 shadow-lg backdrop-blur-sm">
      <h2 className="text-2xl font-bold mb-4">Send Us a Message</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium mb-2">
            Name
          </label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="Your name"
            className="bg-input focus-visible:ring-primary focus-visible:border-none"
            required
          />
        </div>
        <div>
          <label htmlFor="email" className="block text-sm font-medium mb-2">
            Email
          </label>
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="<EMAIL>"
            className="bg-input focus-visible:ring-primary focus-visible:border-none"
            required
          />
        </div>
        <div>
          <label htmlFor="subject" className="block text-sm font-medium mb-2">
            Subject
          </label>
          <Input
            id="subject"
            name="subject"
            value={formData.subject}
            onChange={handleChange}
            placeholder="How can we help?"
            className="bg-input focus-visible:ring-primary focus-visible:border-none"
            required
          />
        </div>
        <div>
          <label htmlFor="message" className="block text-sm font-medium mb-2">
            Message
          </label>
          <TextareaAutosize
            placeholder="Your message"
            className={cn(
              "placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground border border-foreground/20 flex  min-h-16 w-full min-w-0 rounded-md bg-input  dark:bg-input/30 px-3 py-2 text-sm transition-[color,box-shadow] outline-none file:inline-flex file:h-7 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50  resize-none focus-within:outline-none",
              "focus-visible:ring-[3px] focus-visible:border-none focus-visible:ring-primary/75 disabled:cursor-not-allowed disabled:opacity-50",
            )}
            // onKeyDown={onKeyDown}
            id="comment"
            minRows={4}
            maxRows={4}
            autoFocus
            spellCheck={false}
            autoComplete="off"
            autoCorrect="off"
            name="message"
            rows={1}
            required
            value={formData.message}
            onChange={handleChange}
          />
        </div>
        <Button type="submit" className="w-full text-white" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Sending...
            </>
          ) : (
            "Send Message"
          )}
        </Button>
      </form>
    </div>
  );
}
