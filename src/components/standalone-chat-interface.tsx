"use client";

import type React from "react";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send, Loader2, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>rk<PERSON>, X, ArrowLeft } from "lucide-react";
import { cn } from "@/utils/utils";
import { motion } from "motion/react";
import Link from "next/link";
import { useRouter } from "next/navigation";

type Message = {
  id: string;
  content: string;
  sender: "user" | "assistant";
  timestamp: Date;
};

export function StandaloneChatInterface() {
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Focus input on load
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!input.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: input,
      sender: "user",
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);

    // Simulate API call delay
    setTimeout(() => {
      // Add assistant response
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: getMockResponse(input),
        sender: "assistant",
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, assistantMessage]);
      setIsLoading(false);
    }, 1500);
  };

  const getMockResponse = (query: string): string => {
    console.log(query);
    const responses = [
      "I can help you find the right MCP server for your needs. Could you tell me more about your project requirements?",
      "There are several MCP servers that might work for you. Have you considered factors like throughput, latency, and protocol compatibility?",
      "Based on your query, I'd recommend checking out ServerX or ServerY. They both have excellent ratings and support the features you mentioned.",
      "The latest version of that MCP server was released last month with significant performance improvements. Would you like me to share the changelog?",
      "Integration is straightforward with most modern frameworks. You'll need to install the client library and configure the connection parameters.",
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  };

  return (
    <div className="w-full max-w-5xl h-[90vh] relative z-10">
      <div className="absolute -inset-0.5 bg-gradient-to-r from-teal via-amber to-crimson rounded-3xl blur-sm opacity-70 transition duration-1000 animate-pulse"></div>
      <div className="relative glass-effect rounded-3xl shadow-2xl flex flex-col h-full overflow-hidden border border-gray-700/50">
        <div className="p-6 border-b border-gray-700/50 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-teal to-amber flex items-center justify-center">
              <Bot className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold gradient-text">MCP Assistant</h2>
              <p className="text-sm text-gray-400">Powered by advanced AI</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Button
              onClick={() => router.back()}
              variant="ghost"
              className="text-gray-300 hover:text-white hover:bg-gray-700/50"
              size="icon"
            >
              <ArrowLeft className="h-5 w-5" />
              <span className="sr-only">Back</span>
            </Button>
            <Link href="/">
              <Button
                variant="ghost"
                className="text-gray-300 hover:text-white hover:bg-gray-700/50"
                size="icon"
              >
                <X className="h-5 w-5" />
                <span className="sr-only">Close</span>
              </Button>
            </Link>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-6">
          {messages.length > 0 ? (
            <div className="space-y-6">
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className={cn(
                    "flex",
                    message.sender === "user" ? "justify-end" : "justify-start",
                  )}
                >
                  <div className="flex items-start max-w-[80%] gap-3">
                    {message.sender === "assistant" && (
                      <div className="w-10 h-10 rounded-full bg-gradient-to-r from-teal to-amber flex-shrink-0 flex items-center justify-center mt-1">
                        <Bot className="h-5 w-5 text-white" />
                      </div>
                    )}
                    <div
                      className={cn(
                        "rounded-2xl px-5 py-4 shadow-lg",
                        message.sender === "user"
                          ? "bg-gradient-to-r from-amber/90 to-crimson/90 text-white"
                          : "bg-gray-400/80 border border-gray-700/50",
                      )}
                    >
                      <p className="leading-relaxed text-base">{message.content}</p>
                      <p className="text-xs opacity-70 mt-2 text-right">
                        {message.timestamp.toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </p>
                    </div>
                    {message.sender === "user" && (
                      <div className="w-10 h-10 rounded-full bg-gradient-to-r from-amber to-crimson flex-shrink-0 flex items-center justify-center mt-1">
                        <User className="h-5 w-5 text-white" />
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          ) : (
            <div className="h-full flex flex-col items-center justify-center text-center p-4">
              <div className="w-20 h-20 rounded-full bg-gradient-to-r from-teal via-amber to-crimson flex items-center justify-center mb-6">
                <Sparkles className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-3xl font-bold mb-4 gradient-text">How can I assist you today?</h3>
              <p className="text-gray-300 max-w-md text-lg mb-10">
                Ask me anything about MCP servers, integration, or finding the right solution for
                your project.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-2xl">
                {[
                  "What are the best MCP servers for IoT?",
                  "How do I integrate with QuantumMCP?",
                  "Compare NexusMCP vs CloudMCP",
                  "What's new in MCP protocol v5?",
                ].map((suggestion, i) => (
                  <Button
                    key={i}
                    variant="outline"
                    className="justify-start text-left h-auto py-4 px-5 border-gray-700/50 hover:border-teal hover:bg-gray-800/50 text-base"
                    onClick={() => {
                      setInput(suggestion);
                      inputRef.current?.focus();
                    }}
                  >
                    {suggestion}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>

        <form onSubmit={handleSubmit} className="p-6 border-t border-gray-700/50 flex gap-3">
          <Input
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask about MCP servers..."
            className="bg-gray-200/50 border-gray-700/50 focus:border-teal focus-visible:ring-teal py-6 text-base"
          />
          <Button
            type="submit"
            size="icon"
            disabled={isLoading}
            className=" from-teal to-amber hover:opacity-90 transition-opacity h-12 w-12"
          >
            {isLoading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <Send className="h-5 w-5" />
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}
