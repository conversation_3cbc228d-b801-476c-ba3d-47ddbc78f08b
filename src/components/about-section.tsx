import { ServerType } from "@/lib/types";

interface Props {
  server: ServerType;
}
function AboutSection({ server }: Props) {
  return (
    <div className="bg-secondary dark:bg-card rounded-2xl p-8">
      <h2 className="text-4xl text-primary text-center font-bold mb-6">About {server.name}</h2>
      <p className="text-lg mb-6">{server.fullDescription}</p>

      <h3 className="text-xl font-bold mb-3">Features</h3>
      <ul className="list-disc pl-5 space-y-2 mb-6">
        {server.features.map((feature, index) => (
          <li key={index}>{feature}</li>
        ))}
      </ul>

      <h3 className="text-xl font-bold mb-3">Technical Specifications</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-zinc-700/50 p-4 rounded-lg">
          <p className="text-md font-medium">Version</p>
          <p className="text-lg font-bold">{server.version}</p>
        </div>
        <div className="bg-zinc-700/50 p-4 rounded-lg">
          <p className="text-md font-medium">License</p>
          <p className="text-lg font-bold">{server.license}</p>
        </div>
        <div className="bg-zinc-700/50 p-4 rounded-lg">
          <p className="text-md font-medium">Last Updated</p>
          <p className="text-lg font-bold">{server.lastUpdated}</p>
        </div>
        <div className="bg-zinc-700/50 p-4 rounded-lg">
          <p className="text-md font-medium">Protocol</p>
          <p className="text-lg font-bold">{server.protocol}</p>
        </div>
      </div>
    </div>
  );
}

export default AboutSection;
