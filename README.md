<a name="readme-top"></a>

<div align="center">
  <!-- <img src="public/lt-3d-logo.png" alt="logo" width="140"  height="auto" /> -->
  <!-- <br/> -->

   <!-- # Lightray Technologies -->

# MCP Marketplace

</div>

This guide will help you set this projet up locally, and will tell you all you need to know about making contributions to this repository.

## Getting Started

This is a [Next.js v15](https://nextjs.org) project, so before you clone and install the dependencies, make sure you have the following installed in your system.

- [Node](https://nodejs.org) >=20.11.0
- [pnpm](https://pnpm.io/) v10.10.0

It's worth noting that this project has TypeScript ES-Lint that won't work as expected with older versions of Node. It also has a GitHub workflow that depends on an exact version of the `pnpm-lock.yaml` file, whose structure might be changed between pnpm versions. It's important that you install the exact versions listed to avoid errors.

## Stack

As mentioned, this is a [Next.js v15](https://nextjs.org) project that uses [pnpm](https://pnpm.io/) as a package manager. Other features this project includes are:

- [Typescript](https://www.typescriptlang.org/) for type safety.
- [Tailwind v4](https://tailwindcss.com/) for styling.
- [Turbopack](https://nextjs.org/docs/app/api-reference/turbopack), a bundler optimized for JavaScript and TypeScript and built into Next.js for faster local development.
- Full compatibility with [shadcn](https://ui.shadcn.com/) components.\*
- Has [`next-themes`](https://ui.shadcn.com/docs/dark-mode/next) installed with `system` being the default along with `dark` and `light` mode options.
- Eslint {[Next Lint](https://nextjs.org/docs/app/api-reference/config/eslint) and [Typescript-ESLint](https://typescript-eslint.io/)}, [Prettier](https://prettier.io/), and [Stylelint](https://stylelint.io/) to govern code syntax and formatting.
- [Husky](https://github.com/typicode/husky/releases/tag/v9.0.1) + [lint-staged](https://github.com/lint-staged/lint-staged) to run linter checks on files set to be commited to git.

\* _[shadcn](https://ui.shadcn.com/) is the prefered component library for this project and you are strongly encouraged to start your search for components there before looking elsewhere. Because this project uses shadcn color themes, the components will "just work" right out of the box and you wont even have to worry about configuring them for dark/light mode. The prefered way of adding any components you'll need is through the pnpm-cli command._

## Setting Up

Once you have the right Node and pnpm versions, run

> pnpm install --frozen-lockfile

to install the exact versions of the dependencies listed in `pnpm-lock.yaml`. After that, feel free to spin up the development server by running `pnpm dev`.

## Linting

As mentioned before, this project has linters in place to guarantee a degreee of consistency in the code-base across collaboration. To be more specific, we have:

- ESLint for `.ts`, and `.tsx` files
- Stylelint for `.css` files
- Prettier style formatter

To make sure that the lint rules will be observed, the files are checked two separate times.

1. This project has Husky + lint-staged so it will run the linterd on any files you want to commit to git. If any of the linters fail, it will reject the commit. To make the most of this feature, I reccomend using the terminal to commit your changes. For Windows users, both git bash and powershell should work. the important thing is that yoru terminal of choice should be configured to work with git and able to push your changes onto GitHub.

2. When you open a pull-request on GitHub, a GitHub action will be triggered that will run the same tests on the files in the head of the branch with the pull request. If the tests fail, a merge will not be possible.

Both steps have been implemented so that if for whatever reason lint-staged seems to be gettign in your way, you can **temporarily** disable it in your local enviroment but the checks will still happen when you open a pull request.

If you wish to proactively check the linters, or **_auto-fix errors that can be auto-fixed_**, the following commands that have been defined in the `scripts` section of `package.json` will prove usefull.

```bash
  pnpm lint:eslint      # check eslint errors
  pnpm lint:prettier    # check prettier errors
  pnpm lint:stylelint   # check stylelint errors
  pnpm lint:all         # run all linters
  pnpm fix:eslint       # fix eslint errors
  pnpm fix:prettier     # fix prettier errors
  pnpm fix:stylelint    # fix stylelint errors
  pnpm fix:all          # fix all errors
```

## Naming & Formatting Conventions

Below are the naming conventions that are used in this project. Following them will reduce the number of potential run-ins with linter errors.

### 1. File Names

All file names are to use `kebab-case`

```
// ✅
user-profile.tsx
calculate-sum.ts
format-date.ts

// ❌
userProfile.tsx
CalculateSum.ts
format_date.ts
```

### 2. Default Exports in Next.Js App Directory Entry-Point Files

For the `src/app/**/{layout,page,error,loading}.{js,jsx,ts,tsx}` files, **the default export has to be a named function declaration**

```
// ✅
export default function Layout() { … }

// ❌
export default () => { … }
export default function* Generator() { … }
```

### 3. Variables

#### Local

- All local variables have to be in `strictCamelCase`, consecutive upper-case letters are not allowed.

- Leading/trailing single or double underscores allowed (for unused or special variables).

```
// ✅
const userName: string;
const userId: string;
const _tempValue: number;
const __private__: string;

// ❌
const user_name: string;
const userID: string;
const UserName: string;
```

#### Global

- All global variables are to use UPPER_CASE

```
  // ✅
const API_URL = "https://…";
let GLOBAL_COUNT = 0;

// ❌
const api_url = "…";
let globalCount = 0;
let global-count = 0;
```

### 4. Functions

Functions that are default exports should use `StrictPascalCase`. \
 Functions that _are not_ default exports should use `strictCamelCase`

```
// ✅
export default function FetchUserData() { … }
const calculateSum = (a: number, b: number) => a + b;

// ❌
export default function fetchUserData() { … }
const CalsulateSum = () => { … };
```

### 5. Parameters

All function parameter names should use `snake_case`

```
// ✅
function doSomething(user_id: string) { … }
const doSomething = (user_id: string) => { … }

// ❌
function doSomething(userId: string) { … }
const doSomething = (userId: string) => { … }
```

### 6. Interface & Type Declarations

All `interface` and `type` declarations have to use `StrictPAscalCase`

```
// ✅
interface UserProfile { … }
type ApiResponse = { … };

// ❌
interface user_profile { … }
type apiResponse = { … };
```

### 7. Formatting

Most of the formatting rules are handled by Prettier, and can be auto-fixed with the appropriate command (See linting). The list of Prettier rules is quite ling, but the ones worth mentioning are:

- Use double quotes `"`
- Tab width: 2 spaces
- Max line length: 100 characters
- Semicolons are required
- Bracket spacing is required `✅ { space } ❌{nospace}`

<br/>
<br/>

Most develpers will have varying preferences over naming conventions and styling, but for the purposes of this project, these conventions will be the standard so keep them in mind when adding new code or refactoring. If you use VS-Code, the ESLint and Prettier will help you catch some or most of the errors before even running the linters.

If you have any questions or suggestions, feel free to open a PR or issue.
