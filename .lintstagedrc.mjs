// .lintstagedrc.mjs
import path from "path";

const buildEslintCmd = (filenames) => {
  const files = filenames.map((f) => path.relative(process.cwd(), f)).join(" --file ");
  return `pnpm exec next lint --max-warnings=0 --file ${files}`;
};

const buildPrettierCmd = (filenames) => {
  const files = filenames.map((f) => path.relative(process.cwd(), f)).join(" ");
  return `pnpm exec prettier --check ${files}`;
};

const buildStylelintCmd = (filenames) => {
  const files = filenames.map((f) => path.relative(process.cwd(), f)).join(" ");
  return `pnpm exec "stylelint --max-warnings=0 ${files}`;
};

export default {
  "*.{js,jsx,ts,tsx}": [buildEslintCmd, buildPrettierCmd],
  "*.md": [buildPrettierCmd],
  "*.{css,scss}": [buildStylelintCmd, buildPrettierCmd],
};
