import { FlatCompat } from "@eslint/eslintrc";
import eslintPluginUnicorn from "eslint-plugin-unicorn";

const compat = new FlatCompat({ baseDirectory: import.meta.dirname });
const eslintConfig = [
  {
    ignores: [".lintstagedrc.mjs"],
  },

  ...compat.extends("next/core-web-vitals", "next/typescript", "plugin:@next/next/recommended"),
  // {
  //   languageOptions: {
  //     parserOptions: {
  //       projectService: true,
  //       tsconfigRootDir: import.meta.dirname,
  //     },
  //   },
  // },
  {
    plugins: {
      unicorn: eslintPluginUnicorn,
    },
    rules: {
      // prefer single quotes in JSX
      "jsx-quotes": ["error", "prefer-double"],
      "@next/next/no-img-element": ["error"],
      "@typescript-eslint/await-thenable": "off",
      // "@typescript-eslint/naming-convention": [
      //   "error",
      //   // {
      //   //   selector: ["variable"],
      //   //   types: ["number", "string"],
      //   //   format: ["strictCamelCase"],
      //   //   leadingUnderscore: "allowSingleOrDouble",
      //   //   trailingUnderscore: "allowSingleOrDouble",
      //   // },
      //   // {
      //   //   selector: ["variable"],
      //   //   types: ["number", "string"],
      //   //   modifiers: ["global"],
      //   //   format: ["UPPER_CASE"],
      //   // },
      //   // {
      //   //   selector: ["variable"],
      //   //   types: ["function"],
      //   //   format: ["strictCamelCase", "StrictPascalCase"],
      //   // },
      //   {
      //     selector: ["function"],
      //     format: ["strictCamelCase", "StrictPascalCase"],
      //   },
      //   {
      //     selector: ["parameter"],
      //     format: ["snake_case"],
      //     leadingUnderscore: "allowSingleOrDouble",
      //     trailingUnderscore: "allowSingleOrDouble",
      //   },
      //   {
      //     selector: ["interface", "typeAlias"],
      //     format: ["StrictPascalCase"],
      //   },
      // ],
    },
  },
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    rules: {
      "unicorn/filename-case": [
        "error",
        {
          cases: {
            kebabCase: true,
          },
        },
      ],
    },
  },

  {
    files: [
      "src/app/**/layout.{js,jsx,ts,tsx}",
      "src/app/**/page.{js,jsx,ts,tsx}",
      "src/app/**/error.{js,jsx,ts,tsx}",
      "src/app/**/loading.{js,jsx,ts,tsx}",
    ],
    rules: {
      // error if DefaultExport is anything other than a FunctionDeclaration
      "no-restricted-syntax": [
        "error",
        {
          selector: "ExportDefaultDeclaration[declaration.type!='FunctionDeclaration']",
          message: "Default export must be a named function declaration (no arrow or expression)",
        },
      ],
    },
  },
  {
    // Overide for for incoming shadcn components
    files: ["src/components/ui/**/*.{js,jsx,ts,tsx}"],
    rules: {
      "@typescript-eslint/naming-convention": [
        "error",
        {
          selector: ["variable"],
          format: ["camelCase", "PascalCase"],
          leadingUnderscore: "allowSingleOrDouble",
          trailingUnderscore: "allowSingleOrDouble",
        },
        {
          selector: ["parameter"],
          format: ["camelCase"],
          leadingUnderscore: "allowSingleOrDouble",
          trailingUnderscore: "allowSingleOrDouble",
        },
      ],
    },
  },
  {
    // Turn off naming rules for APIs to accomodate different framework requirements
    files: ["**/route.{js,ts}", "src/lib/supabase-server-actions.ts"],
    rules: {
      "@typescript-eslint/naming-convention": "off",
    },
  },
];

export default eslintConfig;
