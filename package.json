{"name": "mcp-marketplace", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.10.0", "engines": {"node": ">=20.11.0"}, "scripts": {"preinstall": "corepack enable && corepack prepare pnpm@10.10.0 --activate", "prepare": "husky", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint:eslint": "next lint", "lint:prettier": "prettier --check .", "lint:stylelint": "stylelint \"**/*.{css,scss}\"", "lint:all": "pnpm run /^lint:.*/", "fix:eslint": "next lint --fix", "fix:prettier": "prettier --write .", "fix:stylelint": "stylelint --fix \"**/*.{css,scss}\"", "fix:all": "pnpm run /^fix:.*/"}, "dependencies": {"@ai-sdk/openai": "1.3.22", "@ai-sdk/react": "1.2.12", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@hookform/resolvers": "5.0.1", "@marsidev/react-turnstile": "1.1.0", "@mui/icons-material": "7.1.0", "@mui/material": "7.1.0", "@radix-ui/react-avatar": "1.1.9", "@radix-ui/react-checkbox": "1.3.1", "@radix-ui/react-dropdown-menu": "2.1.14", "@radix-ui/react-label": "2.1.6", "@radix-ui/react-select": "2.2.4", "@radix-ui/react-slot": "1.2.2", "@radix-ui/react-tabs": "1.1.11", "@radix-ui/react-tooltip": "1.2.7", "@supabase/ssr": "0.6.1", "@supabase/supabase-js": "2.49.8", "ai": "4.3.16", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "highlight.js": "11.11.1", "lucide": "0.509.0", "lucide-react": "0.509.0", "motion": "12.12.1", "next": "15.3.2", "next-themes": "0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.56.4", "react-icons": "5.5.0", "react-markdown": "10.1.0", "react-textarea-autosize": "8.5.9", "rehype-highlight": "7.0.2", "remark-gfm": "4.0.1", "sonner": "2.0.3", "tailwind-merge": "3.2.0", "zod": "3.25.42"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@next/eslint-plugin-next": "15.3.2", "@tailwindcss/postcss": "4.1.5", "@types/node": "22.15.17", "@types/react": "19.1.3", "@types/react-dom": "19.1.3", "@typescript-eslint/eslint-plugin": "8.32.0", "@typescript-eslint/parser": "8.32.0", "eslint": "9.26.0", "eslint-config-next": "15.3.2", "eslint-config-prettier": "10.1.5", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "5.4.0", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-unicorn": "59.0.1", "husky": "9.1.7", "lint-staged": "16.0.0", "prettier": "3.5.3", "stylelint": "16.19.1", "stylelint-config-standard": "38.0.0", "stylelint-order": "7.0.0", "stylelint-prettier": "5.0.3", "tailwindcss": "4.1.5", "tw-animate-css": "1.2.9", "typescript": "5.8.3"}}