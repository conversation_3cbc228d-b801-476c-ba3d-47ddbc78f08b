const config = {
  extends: ["stylelint-config-standard"],
  plugins: ["stylelint-prettier", "stylelint-order"],
  rules: {
    "color-hex-alpha": "never",
    "color-named": "never",
    "color-no-hex": true,
    "property-no-vendor-prefix": null,
    "at-rule-no-deprecated": null,
    "at-rule-no-unknown": null,
    "import-notation": "string",

    // Invoke Prettier formatting
    "prettier/prettier": true,

    // Enforce alphabetical order of properties
    "order/properties-alphabetical-order": true,
  },
  ignoreFiles: ["node_modules/**", ".next/**", "out/**"],
};

export default config;
